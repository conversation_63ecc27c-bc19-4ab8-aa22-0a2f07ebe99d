# ProGuard 混淆规则文件
# 基本配置
-dontwarn
-dontshrink
-dontoptimize
-keepattributes Exceptions,InnerClasses,Signature,Deprecated,SourceFile,LineNumberTable,*Annotation*,EnclosingMethod,LocalVariableTable,LocalVariableTypeTable,MethodParameters
-adaptclassstrings

# 保持主类不混淆
-keep class com.mega.gossipharbor.cloud.ExchangeApplication { *; }

# 保持secure目录下的所有类不混淆
-keep class com.mega.gossipharbor.cloud.secure.** { *; }

# 保持关键接口和抽象类不混淆（避免反射调用问题）
-keep interface com.example.security.KeyProvider { *; }
-keep class com.mega.gossipharbor.cloud.security.KeyProviderException { *; }
-keep class com.mega.gossipharbor.cloud.security.EncryptedService { *; }
-keep class com.mega.gossipharbor.cloud.security.EncryptedClassLoader { *; }
-keep class com.mega.gossipharbor.cloud.config.EncryptedMapperFactoryBean { *; }

# 保持Spring框架相关的类和注解不混淆
-keep class org.springframework.** { *; }
-keep class org.springframework.boot.** { *; }
-keep class org.springframework.web.** { *; }
-keep class org.springframework.context.** { *; }
-keep class org.springframework.beans.** { *; }
-keep class org.springframework.core.** { *; }
-keep class org.springframework.data.** { *; }
-keep class org.springframework.transaction.** { *; }
-keep class org.springframework.aop.** { *; }
-keep class org.springframework.scheduling.** { *; }

# 保持带有Spring注解的类和方法不混淆
-keep @org.springframework.stereotype.Component class * { *; }
-keep @org.springframework.stereotype.Service class * { *; }
-keep @org.springframework.stereotype.Repository class * { *; }
-keep @org.springframework.web.bind.annotation.RestController class * { *; }
-keep @org.springframework.web.bind.annotation.Controller class * { *; }
-keep @org.springframework.boot.autoconfigure.SpringBootApplication class * { *; }
-keep @org.springframework.context.annotation.Configuration class * { *; }

# 保持带有特定注解的方法不混淆
-keepclassmembers class * {
    @org.springframework.web.bind.annotation.RequestMapping *;
    @org.springframework.web.bind.annotation.GetMapping *;
    @org.springframework.web.bind.annotation.PostMapping *;
    @org.springframework.web.bind.annotation.PutMapping *;
    @org.springframework.web.bind.annotation.DeleteMapping *;
    @org.springframework.beans.factory.annotation.Autowired *;
    @org.springframework.beans.factory.annotation.Value *;
    @org.springframework.scheduling.annotation.Scheduled *;
    @org.springframework.context.annotation.Bean *;
}

# 保持Controller类的所有方法参数名不混淆
-keepparameternames
-keepclassmembers @org.springframework.web.bind.annotation.RestController class * {
    public *;
}
-keepclassmembers @org.springframework.web.bind.annotation.Controller class * {
    public *;
}

# 保持接口不混淆
-keepnames interface ** { *; }

# 保持枚举类不混淆
-keepclassmembers enum * { *; }

# 保持序列化相关
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# 保持MyBatis相关
-keep class org.apache.ibatis.** { *; }
-keep class org.mybatis.** { *; }

# 保持Lombok生成的方法
-keep class lombok.** { *; }
-keepclassmembers class * {
    @lombok.* *;
}
