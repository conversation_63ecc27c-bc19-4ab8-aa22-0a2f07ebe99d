package com.mega.gossipharbor.cloud.common.exception;


import com.mega.gossipharbor.cloud.core.exception.BaseException;

import java.util.Objects;

public class CommonException extends BaseException {

    public CommonException(Integer code) {
        this(Objects.requireNonNull(CommonErrorCode.getByCode(code)));
    }

    public CommonException(CommonErrorCode code) {
        super(code.getCode(), code.getMessage());
    }

    public CommonException(Integer code, String message) {
        super(code, message);
    }
}
