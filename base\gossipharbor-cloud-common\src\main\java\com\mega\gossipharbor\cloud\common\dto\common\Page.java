package com.mega.gossipharbor.cloud.common.dto.common;

import lombok.Data;

@Data
public class Page {

    private Integer page;
    private Integer pageSize;

    private Integer limit;
    private Integer offset;

    public Page(Integer page, Integer pageSize) {
        this.page = page;
        this.pageSize = pageSize;
        this.limit = pageSize;
        this.offset = (page - 1) * pageSize;
    }
}
