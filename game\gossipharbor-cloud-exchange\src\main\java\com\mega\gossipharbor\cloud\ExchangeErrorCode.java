package com.mega.gossipharbor.cloud;

import com.mega.gossipharbor.cloud.core.utils.SpringUtils;
import lombok.Getter;

@Getter
public enum ExchangeErrorCode {

    ERR_0(0),
    ITEM_NOT_FOUND(5001), //道具不存在
    INSUFFICIENT_INVENTORY(5002), //库存不足
    UNSUPPORTED_ITEM_TYPE(5003), //不支持的道具类型
    DEDUCT_AMOUNT_MUST_BE_NEGATIVE(5004), //扣除数量必须为负数
    ;

    private final Integer code;

    ExchangeErrorCode(Integer code) {
        this.code = code;
    }

    public static ExchangeErrorCode getExchangeCode(Integer code) {
        for (ExchangeErrorCode exchangeCode : ExchangeErrorCode.values()) {
            if (exchangeCode.getCode().equals(code)) {
                return exchangeCode;
            }
        }
        return null;
    }

    public String getMessage() {
        return SpringUtils.getLocaleMessage(this.code);
    }
}
