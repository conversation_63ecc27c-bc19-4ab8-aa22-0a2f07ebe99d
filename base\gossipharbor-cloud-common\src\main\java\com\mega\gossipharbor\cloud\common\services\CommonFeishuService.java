package com.mega.gossipharbor.cloud.common.services;

import com.mega.gossipharbor.cloud.common.config.FeishuProperties;
import com.mega.gossipharbor.cloud.common.dto.common.DingTalkSlowSqlStatisticsDTO;
import com.mega.gossipharbor.cloud.core.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Date;

@Service
@Slf4j
public class CommonFeishuService {
    private final FeishuProperties feishuProperties;
    private final FeishuService feishuService;
    private final String env;
    private final RestTemplate restTemplate;

    @Autowired
    public CommonFeishuService(FeishuProperties feishuProperties, FeishuService feishuService, @Value("${spring.profiles.active}") String env, RestTemplate restTemplate) {
        this.feishuProperties = feishuProperties;
        this.feishuService = feishuService;
        this.env = env;
        this.restTemplate = restTemplate;
    }

    /**
     * 飞书通用报警 使用commonUrl
     *
     * @param title 标题
     * @param text  报警信息
     */
    public void feishuNotify(String title, String text) {
        try {
            this.feishuService.sendRobotMsgByMarkDownAsync(title, text, feishuProperties.getCommonUrl());
        } catch (Exception e) {
            log.error("飞书通用报警失败", e);
        }
    }

    public void feishuBotSQLAsync(DingTalkSlowSqlStatisticsDTO dingTalkSlowSqlStatisticsDTO) {
        try {
            String text = "**慢查询方法：** " + dingTalkSlowSqlStatisticsDTO.getMethod() + "\n" + "**慢查询sql：** " + dingTalkSlowSqlStatisticsDTO.getSlowsql() + "\n" + "**慢查询时间：** " + dingTalkSlowSqlStatisticsDTO.getCost() + " ms\n" + "<text_tag color='red'> 统计时间：" + DateUtils.formatTime(new Date()) +
                    "</text_tag>\n<text_tag color='red'>[慢查询] [" + env + "环境]</text_tag>";
            this.feishuService.sendRobotMsgByMarkDownAsync("慢查询sql", text, feishuProperties.getSlowSqlUrl());
        } catch (Exception e) {
            log.error("慢查询dingtalk", e);
        }
    }
}
