package com.mega.gossipharbor.cloud.data.service;

import com.mega.gossipharbor.cloud.DataErrorCode;
import com.mega.gossipharbor.cloud.DataException;
import com.mega.gossipharbor.cloud.core.ds.DS;
import com.mega.gossipharbor.cloud.data.dao.GameDataDao;
import com.mega.gossipharbor.cloud.data.vo.GameDataFetchReqVO;
import com.mega.gossipharbor.cloud.data.vo.GameDataFetchRespVO;
import com.mega.gossipharbor.cloud.data.vo.GameDataSaveReqVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

/**
 * 游戏数据业务服务
 * 负责游戏数据的存储和获取业务逻辑
 */
@Service
@Slf4j
public class GameDataService {

    private final GameDataDao gameDataDao;

    @Autowired
    public GameDataService(GameDataDao gameDataDao) {
        this.gameDataDao = gameDataDao;
    }

    /**
     * 保存游戏数据
     * 同时更新主表和插入历史记录
     * 
     * @param reqVO 保存请求参数
     */
    @DS("gameMaster")
    @Transactional(rollbackFor = Exception.class)
    public void saveGameData(GameDataSaveReqVO reqVO) {
        log.info("开始保存游戏数据，characterId={}, gameRegionId={}", 
                reqVO.getCharacterId(), reqVO.getGameRegionId());

        try {
            // 验证JSON数据格式
            if (!StringUtils.hasText(reqVO.getJsonData())) {
                throw new DataException(DataErrorCode.INVALID_JSON_DATA);
            }

            // 更新或插入主表数据
            int mainResult = gameDataDao.insertOrUpdateGameData(
                reqVO.getCharacterId(), 
                reqVO.getJsonData()
            );
            
            if (mainResult <= 0) {
                log.error("更新主表失败，characterId={}", reqVO.getCharacterId());
                throw new DataException(DataErrorCode.DATABASE_OPERATION_FAILED);
            }

            // 插入历史记录
            int historyResult = gameDataDao.insertGameDataHistory(
                reqVO.getCharacterId(), 
                reqVO.getJsonData()
            );
            
            if (historyResult <= 0) {
                log.error("插入历史记录失败，characterId={}", reqVO.getCharacterId());
                throw new DataException(DataErrorCode.DATABASE_OPERATION_FAILED);
            }

            log.info("游戏数据保存成功，characterId={}", reqVO.getCharacterId());

        } catch (DataException e) {
            log.error("保存游戏数据失败，characterId={}, error={}", 
                    reqVO.getCharacterId(), e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("保存游戏数据异常，characterId={}", reqVO.getCharacterId(), e);
            throw new DataException(DataErrorCode.DATABASE_OPERATION_FAILED);
        }
    }

    /**
     * 获取游戏数据
     * 
     * @param reqVO 获取请求参数
     * @return 游戏数据响应
     */
    @DS("gameSlave")
    public GameDataFetchRespVO fetchGameData(GameDataFetchReqVO reqVO) {
        log.info("开始获取游戏数据，characterId={}, gameRegionId={}", 
                reqVO.getCharacterId(), reqVO.getGameRegionId());

        try {
            String jsonData = gameDataDao.selectGameDataByCharacterId(reqVO.getCharacterId());
            
            GameDataFetchRespVO respVO = new GameDataFetchRespVO();
            respVO.setJsonData(jsonData != null ? jsonData : "{}");

            log.info("游戏数据获取成功，characterId={}, hasData={}", 
                    reqVO.getCharacterId(), jsonData != null);

            return respVO;

        } catch (Exception e) {
            log.error("获取游戏数据异常，characterId={}", reqVO.getCharacterId(), e);
            throw new DataException(DataErrorCode.DATABASE_OPERATION_FAILED);
        }
    }
}
