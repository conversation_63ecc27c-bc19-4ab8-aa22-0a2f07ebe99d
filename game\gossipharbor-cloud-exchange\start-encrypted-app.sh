#!/bin/bash

echo "========================================"
echo "GossipHarbor Exchange 加密应用启动脚本"
echo "========================================"
echo "📝 版本: v2.1 (支持端口自动检测)"
echo "📝 更新内容:"
echo "  - 修复Maven依赖解析问题（${revision}变量）"
echo "  - 跳过ProGuard和Docker构建避免Windows兼容性问题"
echo "  - 智能端口检测，自动寻找可用端口"
echo "  - 添加后台运行模式"
echo "========================================"
echo ""

# 配置变量
DEFAULT_PORT=8080
MAX_PORT_ATTEMPTS=20

# 端口检测函数 - 简化版本，更可靠
find_available_port() {
    local port=$DEFAULT_PORT
    local attempts=0

    echo "🔍 检测可用端口..." >&2

    while [ $attempts -lt $MAX_PORT_ATTEMPTS ]; do
        echo "检测端口 $port..." >&2

        # 使用netstat检查端口占用情况
        # Windows下netstat -an的输出格式: TCP    0.0.0.0:8080    0.0.0.0:0    LISTENING
        if netstat -an 2>/dev/null | grep ":$port " >/dev/null 2>&1; then
            echo "⚠️  端口 $port 被占用，尝试下一个端口..." >&2
        else
            echo "✅ 找到可用端口: $port" >&2
            echo "$port"
            return 0
        fi

        port=$((port + 1))
        attempts=$((attempts + 1))
    done

    echo "❌ 错误: 在 $DEFAULT_PORT-$((DEFAULT_PORT + MAX_PORT_ATTEMPTS - 1)) 范围内未找到可用端口" >&2
    exit 1
}

# 环境检查
echo "🔍 检查运行环境..."
if ! command -v java &> /dev/null; then
    echo "❌ 错误: 未找到Java环境，请安装JDK 11或更高版本"
    exit 1
fi

if ! command -v mvn &> /dev/null; then
    echo "❌ 错误: 未找到Maven，请安装Maven 3.6或更高版本"
    exit 1
fi

java_version=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
echo "✅ Java版本: $java_version"

mvn_version=$(mvn -version 2>&1 | head -n 1 | cut -d' ' -f3)
echo "✅ Maven版本: $mvn_version"
echo ""

# 执行打包和加密文件准备
echo "0. 准备应用..."
# 清理旧的jar包和加密文件
echo "清理旧文件..."
rm -rf target
rm -rf encrypted

# 执行Maven打包
echo "使用Maven构建项目..."
# 首先确保父POM和依赖模块已安装到本地仓库
echo "安装父POM和依赖模块..."
cd ../..

# 检查依赖模块是否已存在（避免不必要的重复构建）
if [ -f "$HOME/.m2/repository/com/mega/gossipharbor/cloud/gossipharbor-cloud-common/1.0/gossipharbor-cloud-common-1.0.jar" ]; then
    echo "✅ 依赖模块已存在，直接构建exchange模块"
else
    echo "⚠️  依赖模块不存在，但继续尝试构建（可能已在本地仓库中）"
fi

# 使用reactor构建方式构建exchange模块（解决依赖解析问题）
# 不使用clean避免文件锁定问题，跳过ProGuard和Docker避免Windows兼容性问题
echo "构建exchange模块..."
mvn package -pl :gossipharbor-cloud-exchange -am -DskipTests "-Dencrypt.key=${ENCRYPT_KEY}"
if [ $? -ne 0 ]; then
    echo "❌ 错误: Maven构建失败"
    echo "💡 提示: 如果遇到依赖解析问题，请手动执行以下命令："
    echo "   mvn install -N"
    echo "   mvn install -pl base/gossipharbor-cloud-core,base/gossipharbor-cloud-common -DskipTests"
    exit 1
fi

cd game/gossipharbor-cloud-exchange
# 混淆后jar包替换原始jar包
echo "混淆后jar包替换原始jar包..."
mv target/gossipharbor-cloud-exchange-1.0.jar target/gossipharbor-cloud-exchange-1.0-bak.jar
mv target/gossipharbor-cloud-exchange-1.0-obfuscated.jar target/gossipharbor-cloud-exchange-1.0.jar


# 复制加密文件
echo "复制加密文件..."
rm -rf encrypted
cp -r target/encrypted encrypted/
if [ $? -ne 0 ]; then
    echo "❌ 错误: 复制加密文件失败"
    exit 1
fi

# 检查必要文件是否存在
echo "1. 检查文件完整性..."

if [ ! -f "target/gossipharbor-cloud-exchange-1.0.jar" ]; then
    echo "❌ 错误: 主JAR包不存在 - target/gossipharbor-cloud-exchange-1.0.jar"
    echo "请检查Maven构建是否成功"
    exit 1
fi
echo "✅ 主JAR包存在: target/gossipharbor-cloud-exchange-1.0.jar"

if [ ! -d "encrypted/com/mega/gossipharbor/cloud/exchange/secure" ]; then
    echo "❌ 错误: 加密文件目录不存在 - encrypted/com/mega/gossipharbor/cloud/exchange/secure"
    echo "请检查加密过程是否成功"
    exit 1
fi
echo "✅ 加密文件目录存在: encrypted/com/mega/gossipharbor/cloud/exchange/secure"

# 统计加密文件数量
encrypted_class_count=$(find encrypted/com/mega/gossipharbor/cloud/exchange/secure -name "*.class" 2>/dev/null | wc -l)
encrypted_xml_count=$(find encrypted/mapper -name "*.xml" 2>/dev/null | wc -l)
total_encrypted=$((encrypted_class_count + encrypted_xml_count))
echo "✅ 发现 $encrypted_class_count 个加密类文件和 $encrypted_xml_count 个加密XML文件，共 $total_encrypted 个加密文件"

# 验证构建成功的关键指标
if [ $total_encrypted -eq 0 ]; then
    echo "❌ 警告: 没有发现加密文件，请检查构建过程"
    exit 1
fi

# 验证加密文件是否真的加密
echo "验证加密文件状态..."
# 检查Service类
if [ -d "encrypted/com/mega/gossipharbor/cloud/exchange/secure/service" ]; then
    for file in encrypted/com/mega/gossipharbor/cloud/exchange/secure/service/*.class; do
        if [ -f "$file" ]; then
            if file "$file" | grep -q "Java class"; then
                echo "❌ 警告: $(basename "$file") 可能未加密 (仍为Java class格式)"
                exit 1
            else
                echo "✅ $(basename "$file") 已正确加密"
            fi
        fi
    done
fi

# 检查其他类文件
for dir in dao dto; do
    if [ -d "encrypted/com/mega/gossipharbor/cloud/exchange/secure/$dir" ]; then
        for file in encrypted/com/mega/gossipharbor/cloud/exchange/secure/$dir/*.class; do
            if [ -f "$file" ]; then
                if file "$file" | grep -q "Java class"; then
                    echo "❌ 警告: $(basename "$file") 可能未加密 (仍为Java class格式)"
                    exit 1
                else
                    echo "✅ $(basename "$file") 已正确加密"
                fi
            fi
        done
    fi
done

echo ""
echo "2. 文件结构概览:"
echo "├── target/gossipharbor-cloud-exchange-1.0.jar  (主应用JAR - 不含敏感类)"
echo "├── encrypted/com/mega/gossipharbor/cloud/exchange/secure/ (加密的敏感类文件)"
echo "│   ├── service/                             (Service层)"
echo "│   │   ├── DynamicDataSourceService.class  (AES加密)"
echo "│   │   └── TransactionService.class        (AES加密)"
echo "│   ├── dao/                                 (Dao层)"
echo "│   │   └── TransactionDao.class             (AES加密)"
echo "│   └── dto/                                 (DTO层)"
echo "│       └── ItemTurnoverRecord.class        (AES加密)"
echo "├── encrypted/mapper/                        (加密的XML配置文件)"
echo "│   └── TransactionDao.xml                   (AES加密)"
echo "└── target/dependency/                       (Spring Boot依赖JAR包)"

echo ""
echo "3. 启动模式选择:"
echo "[1] 生产模式 (使用默认密钥)"
echo "[2] 调试模式 (详细日志输出)"
echo "[3] 自定义密钥模式"
echo "[4] 后台运行模式"
read -p "请选择启动模式 (1-4): " mode

echo ""
echo "4. 检测可用端口..."
AVAILABLE_PORT=$(find_available_port)

echo ""
echo "5. 启动应用..."
echo "📝 提示: 应用将在端口 $AVAILABLE_PORT 启动"
echo "📝 提示: 启动后可访问 http://localhost:$AVAILABLE_PORT/actuator/health 检查应用状态"
echo ""

case $mode in
    1)
        echo "🚀 启动模式: 生产模式 (使用默认密钥)"
        echo "命令: java -jar target/gossipharbor-cloud-exchange-1.0.jar --server.port=$AVAILABLE_PORT"
        java -jar target/gossipharbor-cloud-exchange-1.0.jar --server.port=$AVAILABLE_PORT
        ;;
    2)
        echo "🚀 启动模式: 调试模式 (详细日志输出)"
        echo "命令: java -Dencrypt.debug=true -jar target/gossipharbor-cloud-exchange-1.0.jar --server.port=$AVAILABLE_PORT"
        java -Dencrypt.debug=true -jar target/gossipharbor-cloud-exchange-1.0.jar --server.port=$AVAILABLE_PORT
        ;;
    3)
        read -p "请输入解密密钥: " custom_key
        echo "🚀 启动模式: 自定义密钥模式"
        echo "命令: java -Ddecrypt.key=$custom_key -jar target/gossipharbor-cloud-exchange-1.0.jar --server.port=$AVAILABLE_PORT"
        java -Ddecrypt.key="$custom_key" -jar target/gossipharbor-cloud-exchange-1.0.jar --server.port=$AVAILABLE_PORT
        ;;
    4)
        echo "🚀 启动模式: 后台运行模式"
        echo "命令: nohup java -jar target/gossipharbor-cloud-exchange-1.0.jar --server.port=$AVAILABLE_PORT > app.log 2>&1 &"
        nohup java -jar target/gossipharbor-cloud-exchange-1.0.jar --server.port=$AVAILABLE_PORT > app.log 2>&1 &
        echo "✅ 应用已在后台启动，日志输出到 app.log"
        echo "📝 使用 'tail -f app.log' 查看实时日志"
        echo "📝 使用 'ps aux | grep gossipharbor-cloud-exchange' 查看进程"
        ;;
    *)
        echo "❌ 无效选择，使用默认生产模式"
        java -jar target/gossipharbor-cloud-exchange-1.0.jar --server.port=$AVAILABLE_PORT
        ;;
esac

echo ""
echo "========================================"
echo "GossipHarbor Exchange 应用已退出"
echo "========================================"
echo ""
echo "📝 故障排除提示:"
echo "1. 端口检测: 脚本会自动从 $DEFAULT_PORT 开始寻找可用端口"
echo "2. 如果遇到依赖解析问题，请删除 ~/.m2/repository/com/mega/gossipharbor 目录后重试"
echo "3. 如果加密文件缺失，请检查Maven构建日志中的加密步骤"
echo "4. 应用日志位置: 当前目录下的 app.log 文件（后台模式）"
echo ""
echo "📝 验证应用状态:"
echo "- 健康检查: curl http://localhost:$AVAILABLE_PORT/actuator/health"
echo "- 查看进程: ps aux | grep gossipharbor-cloud-exchange"
echo "- 查看端口: netstat -tlnp | grep $AVAILABLE_PORT"
echo ""
