# ProGuard配置文件 - 简化版本解决Windows命令行长度限制

# 基本配置 - 最小化配置减少命令行长度
-dontwarn
-dontshrink
-dontoptimize
-keepattributes *Annotation*,Signature
-adaptclassstrings

# 保持主类不混淆
-keep class com.mega.gossipharbor.cloud.ExchangeApplication { *; }

# 保持Spring Boot相关类不混淆
-keep @org.springframework.boot.autoconfigure.SpringBootApplication class * { *; }
-keep @org.springframework.stereotype.Controller class * { *; }
-keep @org.springframework.web.bind.annotation.RestController class * { *; }
-keep @org.springframework.stereotype.Service class * { *; }
-keep @org.springframework.stereotype.Repository class * { *; }
-keep @org.springframework.stereotype.Component class * { *; }
-keep @org.springframework.context.annotation.Configuration class * { *; }

# 保持Spring相关注解的类和方法
-keepclassmembers class * {
    @org.springframework.web.bind.annotation.RequestMapping *;
    @org.springframework.web.bind.annotation.GetMapping *;
    @org.springframework.web.bind.annotation.PostMapping *;
    @org.springframework.web.bind.annotation.PutMapping *;
    @org.springframework.web.bind.annotation.DeleteMapping *;
    @org.springframework.beans.factory.annotation.Autowired *;
    @org.springframework.beans.factory.annotation.Value *;
}

# 保持MyBatis相关类不混淆
-keep interface * extends org.apache.ibatis.annotations.Mapper { *; }
-keep class * implements org.apache.ibatis.annotations.Mapper { *; }

# 保持实体类不混淆（避免序列化问题）
-keep class * extends java.io.Serializable { *; }
-keep class **.*VO { *; }
-keep class **.*DTO { *; }
-keep class **.*Entity { *; }
-keep class **.*DO { *; }

# 保持枚举类不混淆
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# 保持异常类不混淆
-keep class * extends java.lang.Exception { *; }

# 保持反射相关
-keepclassmembers class * {
    public <init>(...);
}

# 保持加密相关的类不混淆（重要）
-keep class com.mega.gossipharbor.cloud.exchange.security.** { *; }
-keep class com.mega.gossipharbor.cloud.exchange.config.Encrypted** { *; }

# 保持加密的DAO和Service接口不混淆
-keep interface com.mega.gossipharbor.cloud.exchange.secure.dao.** { *; }
-keep interface com.mega.gossipharbor.cloud.exchange.secure.service.** { *; }
