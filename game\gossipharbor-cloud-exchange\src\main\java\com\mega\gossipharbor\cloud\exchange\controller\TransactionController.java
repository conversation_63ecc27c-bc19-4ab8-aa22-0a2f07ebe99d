package com.mega.gossipharbor.cloud.exchange.controller;

import com.mega.gossipharbor.cloud.common.client.GossipharborExchangeClient;
import com.mega.gossipharbor.cloud.common.vo.ExchangeTransactionReqVO;
import com.mega.gossipharbor.cloud.core.Result;
import com.mega.gossipharbor.cloud.core.Results;
import com.mega.gossipharbor.cloud.exchange.secure.service.TransactionService;
import com.mega.gossipharbor.cloud.exchange.security.PerformanceMonitor;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 交易控制器
 * 负责处理道具交易相关的API请求
 */
@RestController
@RequestMapping("/exchange/api")
@Api(tags = "道具交易管理")
@Slf4j
public class TransactionController implements GossipharborExchangeClient {

    // 通过ApplicationContext动态获取加密的Service类，避免编译时依赖
    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private PerformanceMonitor performanceMonitor;

    // @Autowired
    // public TransactionController(TransactionService transactionService) {
    //     this.transactionService = transactionService;
    // }

    /**
     * 辅助方法：动态调用加密服务的方法
     * 处理跨类加载器调用的问题
          * @throws Exception 
          */
         private Object callEncryptedService(String beanName, String methodName, Class<?>[] paramTypes, Object... args) throws Exception {
        try {
            // 获取加密的Service Bean
            Object service = applicationContext.getBean(beanName);
            Class<?> serviceClass = service.getClass();

            log.info("Calling encrypted service: {} -> {}", beanName, methodName);
            log.info("Service class: {}", serviceClass.getName());
            log.info("Service classloader: {}", serviceClass.getClassLoader().getClass().getName());

            // 由于跨类加载器问题，需要通过Service的ClassLoader来获取正确的参数类型
            ClassLoader serviceClassLoader = serviceClass.getClassLoader();

            // 重新加载参数类型，使用Service的ClassLoader
            Class<?>[] serviceParamTypes = new Class<?>[paramTypes.length];
            for (int i = 0; i < paramTypes.length; i++) {
                if (paramTypes[i] != null) {
                    // 使用Service的ClassLoader加载参数类型
                    serviceParamTypes[i] = serviceClassLoader.loadClass(paramTypes[i].getName());
                } else {
                    serviceParamTypes[i] = null;
                }
            }

            // 获取方法
            java.lang.reflect.Method method = serviceClass.getMethod(methodName, serviceParamTypes);

            // 调用方法
            return method.invoke(service, args);

        } catch (Exception e) {
            log.error("Error calling encrypted service: {} -> {}", beanName, methodName, e);
            throw e;
        }
    }


    /**
     * 处理道具交易
     * @param reqVO 交易请求参数
     * @return 处理结果
          * @throws Exception 
          */
         @PostMapping("/transaction")
         @ApiOperation("道具交易处理")
         public Result<?> processTransaction(@Validated @RequestBody ExchangeTransactionReqVO reqVO) throws Exception {
        log.info("收到道具交易请求，characterId={}, transactionId={}", reqVO.getCharacterId(), reqVO.getTransactionId());
        callEncryptedService("transactionService", "processTransaction", new Class[]{ExchangeTransactionReqVO.class}, reqVO);
        return Results.success();
       
    }



    /**
     * 实现GossipharborExchangeClient接口
     */
    @Override
    public Result<?> exchangeTransaction(ExchangeTransactionReqVO reqVO) throws Exception {
        log.info("收到道具交易请求，characterId={}, transactionId={}", reqVO.getCharacterId(), reqVO.getTransactionId());
        // transactionService.processTransaction(reqVO);

        callEncryptedService("transactionService", "processTransaction", new Class[]{ExchangeTransactionReqVO.class}, reqVO);
        return Results.success();
    }
}