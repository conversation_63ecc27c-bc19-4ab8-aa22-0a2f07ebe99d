package com.mega.gossipharbor.cloud.common.filter;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.mega.gossipharbor.cloud.common.schedule.AppTokenManagerClient;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Component
@Slf4j
public class FeignTokenRequestInterceptor implements RequestInterceptor {
    private final AppTokenManagerClient appTokenManagerClient;

    public FeignTokenRequestInterceptor(AppTokenManagerClient appTokenManagerClient) {
        this.appTokenManagerClient = appTokenManagerClient;
    }

    @Override
    public void apply(RequestTemplate requestTemplate) {
        String token = null;
        try {
            token = appTokenManagerClient.getToken();
            log.info("apply token:" + token);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        if (StringUtils.hasText(token)) {
            requestTemplate.header("Authorization", "Bearer " + token);
        }
    }
}
