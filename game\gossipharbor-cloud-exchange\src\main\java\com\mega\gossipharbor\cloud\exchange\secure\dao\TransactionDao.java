package com.mega.gossipharbor.cloud.exchange.secure.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.mega.gossipharbor.cloud.exchange.secure.dto.ItemTurnoverRecord;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 交易数据访问层
 * 负责道具库存和流水的数据库操作
 */
@Mapper
public interface TransactionDao {

    /**
     * 根据道具ID查询道具类型编码
     * @param itemId 道具ID
     * @return 道具类型编码(Currency/Consumable/Material等)
     */
    String selectItemCateCodeByItemId(@Param("itemId") Long itemId);

    /**
     * 查询角色货币库存
     * @param characterId 角色ID
     * @param itemId 道具ID
     * @return 当前数量
     */
    Long selectCharacterCurrencyAmount(@Param("characterId") Long characterId, @Param("itemId") Long itemId);

    /**
     * 查询角色消耗品库存
     * @param characterId 角色ID
     * @param itemId 道具ID
     * @return 当前数量
     */
    Long selectCharacterConsumableAmount(@Param("characterId") Long characterId, @Param("itemId") Long itemId);

    /**
     * 查询角色材料库存
     * @param characterId 角色ID
     * @param itemId 道具ID
     * @return 当前数量
     */
    Long selectCharacterMaterialAmount(@Param("characterId") Long characterId, @Param("itemId") Long itemId);

    /**
     * 更新或插入角色货币库存
     * @param characterId 角色ID
     * @param itemId 道具ID
     * @param changeAmount 变更数量
     * @param createTime 创建时间戳
     */
    void upsertCharacterCurrency(@Param("characterId") Long characterId, @Param("itemId") Long itemId, 
                                @Param("changeAmount") Long changeAmount);

    /**
     * 更新或插入角色消耗品库存
     * @param characterId 角色ID
     * @param itemId 道具ID
     * @param changeAmount 变更数量
     */
    void upsertCharacterConsumable(@Param("characterId") Long characterId, @Param("itemId") Long itemId, 
                                  @Param("changeAmount") Long changeAmount);

    /**
     * 更新或插入角色材料库存
     * @param characterId 角色ID
     * @param itemId 道具ID
     * @param changeAmount 变更数量
     */
    void upsertCharacterMaterial(@Param("characterId") Long characterId, @Param("itemId") Long itemId, 
                                @Param("changeAmount") Long changeAmount);
    /**
     * 检查表是否存在
     * @param tableName 表名
     * @return 存在返回1，不存在返回0
     */
    Integer checkTableExists(@Param("tableName") String tableName);

    /**
     * 创建新的item_turnover表
     * @param tableName 表名
     */
    void createItemTurnoverTable(@Param("tableName") String tableName);

    /**
     * 批量插入道具流水记录
     * @param tableName 表名
     * @param records 流水记录列表
     */
    void batchInsertItemTurnover(@Param("tableName") String tableName, @Param("records") List<ItemTurnoverRecord> records);
}