gossipharbor-cloud/
├── account/
│   └── gossipharbor-cloud-account/     # 账号体系模块（用户注册、登录、账号管理等）
├── base/
│   ├── gossipharbor-cloud-common/      # 公共工具模块（工具类、通用封装、常量等）
│   ├── gossipharbor-cloud-core/        # 核心基础模块（领域核心、基础服务等）
│   └── gossipharbor-cloud-generator/   # 代码生成器模块（自动代码、数据、脚本生成等）
├── game/
│   ├── gossipharbor-cloud-data/        # 游戏核心数据模块（游戏业务数据、存储、相关操作等）
│   └── gossipharbor-cloud-exchange/    # 物品/资源交换相关模块（交易所、市场等功能）
├── management/
│   ├── gossipharbor-cloud-admin/       # 后台管理系统模块（管理员操作后台、权限等）
│   └── gossipharbor-cloud-monitor/     # 服务监控与运维模块（监控、告警、日志采集等）
├── .gitignore                          # Git 忽略规则配置
├── gossipharbor-cloud.sh               # 一键部署/运行脚本
├── pom.xml                             # Maven 多模块主配置文件
├── README.md                           # 项目说明文档

# Config
## application.yml
### application.name命名
#### 常规模块下的命名 `${module.name}-${spring.profiles.active}`,对应模块：
1. `gossipharbor-cloud-account`
2. `gossipharbor-cloud-admin`
3. `gossipharbor-cloud-monitor`
#### game包中分区模块命名 `${module.name}-${region.id}-${spring.profiles.active}`,对应模块：
1. `gossipharbor-cloud-data`
2. `gossipharbor-cloud-exchange`