package com.mega.gossipharbor.cloud;

import com.mega.gossipharbor.cloud.core.exception.BaseException;

import java.util.Objects;

public class DataException extends BaseException {

    public DataException(Integer code) {
        this(Objects.requireNonNull(DataErrorCode.getExchangeCode(code)));
    }

    public DataException(DataErrorCode code) {
        super(code.getCode(), code.getMessage());
    }

    public DataException(Integer code, String message) {
        super(code, message);
    }
}
