package com.mega.gossipharbor.cloud;


import com.mega.gossipharbor.cloud.core.exception.BaseException;

import java.util.Objects;

public class AdminException extends BaseException {

    public AdminException(Integer code) {
        this(Objects.requireNonNull(AdminErrorCode.getExchangeCode(code)));
    }

    public AdminException(AdminErrorCode code) {
        super(code.getCode(), code.getMessage());
    }

    public AdminException(Integer code, String message) {
        super(code, message);
    }
}
