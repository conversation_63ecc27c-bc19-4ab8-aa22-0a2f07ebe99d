package com.mega.gossipharbor.cloud;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import tk.mybatis.spring.annotation.MapperScan;

@SpringBootApplication
@EnableDiscoveryClient
@ConfigurationPropertiesScan
@MapperScan(basePackages = {"com.mega.**.mapper", "com.mega.**.dao"})
@EnableFeignClients(basePackages = {"com.mega.platform.cloud.client"})
@ComponentScan(basePackages = {
    "com.mega.gossipharbor.cloud.data",
    "com.mega.gossipharbor.cloud.common",
    "com.mega.gossipharbor.cloud.core"
})
public class DataApplication {

    public static void main(String[] args) {
        SpringApplication.run(DataApplication.class, args);
    }
}
