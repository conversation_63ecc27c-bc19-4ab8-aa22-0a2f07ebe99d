package com.mega.gossipharbor.cloud.common.filter;


import com.mega.gossipharbor.cloud.common.constant.JwtConstants;
import com.mega.gossipharbor.cloud.common.utils.JwtTokenUtil;
import com.mega.gossipharbor.cloud.common.vo.BaseAccountReqVO;
import com.mega.platform.cloud.data.vo.BaseReqVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Collections;

@Component("JwtAuthenticationFilter")
@Slf4j
public class JwtAuthenticationFilter extends OncePerRequestFilter {
    @Autowired
    private JwtTokenUtil jwtTokenUtil;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String token = request.getHeader(JwtConstants.HEADER_AUTH);
        if (StringUtils.hasText(token) && token.startsWith(JwtConstants.TOKEN_PREFIX)) {
            try {
                token = token.substring(JwtConstants.TOKEN_PREFIX.length());
                BaseAccountReqVO baseReqVO = jwtTokenUtil.parseToken(token);
                request.setAttribute(JwtConstants.CLAIM_ACCOUNT_ID, baseReqVO.getAccountId());

                UsernamePasswordAuthenticationToken authentication =
                        new UsernamePasswordAuthenticationToken(baseReqVO, null, Collections.emptyList());
                authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                SecurityContextHolder.getContext().setAuthentication(authentication);
            } catch (Exception e) {
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                return;
            }
        }

        filterChain.doFilter(request, response);
    }
}

