package com.mega.gossipharbor.cloud;

import com.mega.gossipharbor.cloud.exchange.security.EncryptedClassLoader;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

import lombok.extern.slf4j.Slf4j;
import tk.mybatis.spring.annotation.MapperScan;

import java.net.URL;
import java.nio.file.Path;
import java.nio.file.Paths;

@SpringBootApplication
@EnableDiscoveryClient
@ConfigurationPropertiesScan
@MapperScan(basePackages = {"com.mega.**.mapper", "com.mega.**.dao"})
@EnableFeignClients(basePackages = {"com.mega.platform.cloud.client"})
@Slf4j
public class ExchangeApplication {

    public static void main(String[] args) {
        // SpringApplication.run(ExchangeApplication.class, args);

         try {
            log.info("🚀 [MAIN] === Starting with Encrypted ClassLoader ===");

            // 1. 创建自定义ClassLoader
            Path encryptedDir = Paths.get("encrypted");
            URL[] urls = {}; // 空的URL数组，不添加加密目录到classpath

            log.info("🚀 [MAIN] Encrypted directory: " + encryptedDir.toAbsolutePath());

            // 2. 创建自定义ClassLoader
            ClassLoader currentClassLoader = ExchangeApplication.class.getClassLoader();
            EncryptedClassLoader encryptedLoader = new EncryptedClassLoader(urls, currentClassLoader);

            // 3. 设置线程上下文ClassLoader
            Thread.currentThread().setContextClassLoader(encryptedLoader);

            // 4. 使用自定义ClassLoader启动Spring Boot
            SpringApplication app = new SpringApplicationBuilder(ExchangeApplication.class)
                    .build();

            log.info("🚀 [MAIN] === Spring Boot Starting with Encrypted ClassLoader ===");
            app.run(args);

        } catch (Exception e) {
            log.error("Failed to start application with encrypted ClassLoader: {}", e.getMessage(), e);
            e.printStackTrace(); // 打印完整堆栈跟踪

            // 不要降级启动，直接退出以便调试
            System.exit(1);
        }
    }
}
