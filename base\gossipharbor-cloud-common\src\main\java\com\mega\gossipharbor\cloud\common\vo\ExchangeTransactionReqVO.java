package com.mega.gossipharbor.cloud.common.vo;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("物品增加减少VO")
public class ExchangeTransactionReqVO {
    @ApiModelProperty("character_id")
    private Long characterId;

    @ApiModelProperty(value = "游戏大区id", example = "1001")
    private Long gameRegionId;

    @ApiModelProperty("business_dic_id int类型")
    private Long businessDicId;

    @ApiModelProperty("transaction_id varchar类型(订单号、交易号、掉落记录ID等)")
    private String transactionId;

    @ApiModelProperty("增加道具列表 {道具id 道具类型 道具数量 特定道具-增加唯一uuid}")
    private List<ItemVO> addItemList;

    @ApiModelProperty("减少道具列表 {道具id 道具类型 道具数量}")
    private List<ItemVO> reduceItemList;

    @ApiModelProperty("时间戳 发送者要求的时间 秒级时间戳")
    private Long timestamp;


    @Data
    @ApiModel("物品")
    public static class ItemVO{
        
        @ApiModelProperty("道具id")
        private Long itemId;
        
        @ApiModelProperty("道具类型id")
        private Integer itemCateId;
        
        @ApiModelProperty("增加或减少道具数量,增加为正，减少为负")
        private Long itemNum;

        @ApiModelProperty("特定道具-增加唯一uuid")
        private String itemUuid;
    }
}
