<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.mega.gossipharbor.cloud</groupId>
        <artifactId>gossipharbor-cloud</artifactId>
        <version>${revision}</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>

    <artifactId>gossipharbor-cloud-exchange</artifactId>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.mega.gossipharbor.cloud</groupId>
            <artifactId>gossipharbor-cloud-common</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-consul-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>

        <!-- MyBatis Spring Boot Starter -->
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>2.2.2</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>

            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>dockerfile-maven-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>pl.project13.maven</groupId>
                <artifactId>git-commit-id-plugin</artifactId>
                <configuration>
                    <offline>true</offline>
                </configuration>
            </plugin>

            <!-- Maven编译插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <source>11</source>
                    <target>11</target>
                </configuration>
            </plugin>

            <!-- 复制需要加密的类文件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <id>copy-secure-classes</id>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <target>
                                <echo message="Copying secure classes for encryption..."/>
                                <mkdir dir="${project.build.directory}/plain"/>
                                <copy todir="${project.build.directory}/plain" preservelastmodified="true">
                                    <fileset dir="${project.build.outputDirectory}">
                                        <include name="**/secure/**/*.class"/>
                                    </fileset>
                                </copy>
                                <echo message="Copied ${toString:copied.files} files"/>
                            </target>
                        </configuration>
                    </execution>

                </executions>
            </plugin>

            <!-- 执行类文件加密 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>exec-maven-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <id>encrypt-classes</id>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>java</goal>
                        </goals>
                        <configuration>
                            <mainClass>com.mega.gossipharbor.cloud.exchange.mvnbuild.EncryptTool</mainClass>
                            <arguments>
                                <argument>${project.build.directory}/plain</argument>
                                <argument>${project.build.directory}/encrypted</argument>
                            </arguments>
                            <systemProperties>
                                <systemProperty>
                                    <key>encrypt.key</key>
                                    <value>${encrypt.key}</value>
                                </systemProperty>
                            </systemProperties>
                        </configuration>
                    </execution>

                    <!-- 加密XML文件 -->
                    <execution>
                        <id>encrypt-xml-files</id>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>java</goal>
                        </goals>
                        <configuration>
                            <mainClass>com.mega.gossipharbor.cloud.exchange.mvnbuild.EncryptTool</mainClass>
                            <arguments>
                                <argument>${project.build.outputDirectory}/mapper</argument>
                                <argument>${project.build.directory}/encrypted/mapper</argument>
                            </arguments>
                            <systemProperties>
                                <systemProperty>
                                    <key>encrypt.key</key>
                                    <value>${encrypt.key}</value>
                                </systemProperty>
                            </systemProperties>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- 资源处理插件 - 排除Java源文件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.3.1</version>
                <configuration>
                    <excludes>
                        <exclude>**/*.java</exclude>
                        <exclude>**/secure/**/*.java</exclude>
                    </excludes>
                </configuration>
            </plugin>

            <!-- 从JAR中排除原始的敏感类文件和Java源文件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.3.0</version>
                <configuration>
                    <excludes>
                        <exclude>**/*.java</exclude>
                        <exclude>**/secure/**/*.class</exclude>
                        <exclude>**/secure/**/*.java</exclude>
                        <exclude>mapper/*.xml</exclude>
                    </excludes>
                </configuration>
            </plugin>


            <!-- ProGuard 插件 - 启用代码混淆 -->
            <!-- <plugin>
                <groupId>com.github.wvengen</groupId>
                <artifactId>proguard-maven-plugin</artifactId>
                <version>2.7.0</version>
                <executions>
                    <execution>
                        <id>proguard</id>
                        <phase>package</phase>
                        <goals>
                            <goal>proguard</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <useCommandFile>true</useCommandFile>
                    <javaExecutable>${java.home}/bin/java</javaExecutable>
                    <obfuscate>true</obfuscate>
                    <injar>${project.build.finalName}.jar</injar>
                    <outjar>${project.build.finalName}.jar</outjar>
                    <outputDirectory>${project.build.directory}</outputDirectory>
                    <proguardInclude>${project.basedir}/proguard-rules.pro</proguardInclude>
                    <libs>
                        <lib>${java.home}/lib/jrt-fs.jar</lib>
                    </libs>
                </configuration>
            </plugin> -->

            <!-- 1. 生成 proguard-args.txt -->
                <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                    <id>generate-proguard-args</id>
                    <phase>prepare-package</phase>
                    <configuration>
                        <target>
                        <echo file="${project.basedir}/proguard-args.txt">-injars target/${project.build.finalName}.jar
-outjars target/${project.build.finalName}-obfuscated.jar
-libraryjars ${java.home}/lib/jrt-fs.jar
-include ${project.basedir}/proguard-rules.pro</echo>
                        </target>
                    </configuration>
                    <goals>
                        <goal>run</goal>
                    </goals>
                    </execution>
                </executions>
                </plugin>

                <!-- 2. 运行 ProGuard 手动方式 -->
                <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>exec-maven-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                    <id>run-proguard</id>
                    <phase>package</phase>
                    <goals>
                        <goal>exec</goal>
                    </goals>
                    <configuration>
                        <executable>${java.home}/bin/java</executable>
                        <workingDirectory>${project.basedir}</workingDirectory>
                        <arguments>
                        <argument>-jar</argument>
                        <argument>${project.basedir}/tools/proguard/proguard.jar</argument>
                        <argument>@${project.basedir}/proguard-args.txt</argument>
                        </arguments>
                    </configuration>
                    </execution>
                </executions>
                </plugin>


            <!-- Spring Boot 打包插件 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                        <configuration>
                            <mainClass>com.mega.gossipharbor.cloud.ExchangeApplication</mainClass>
                        </configuration>
                    </execution>
                </executions>
            </plugin>


            


        </plugins>
    </build>

    <!-- Maven Profiles -->

</project>