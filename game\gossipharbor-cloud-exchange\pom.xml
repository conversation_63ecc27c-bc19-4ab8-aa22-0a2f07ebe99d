<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.mega.gossipharbor.cloud</groupId>
        <artifactId>gossipharbor-cloud</artifactId>
        <version>${revision}</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>

    <artifactId>gossipharbor-cloud-exchange</artifactId>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.mega.gossipharbor.cloud</groupId>
            <artifactId>gossipharbor-cloud-common</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-consul-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>

        <!-- MyBatis Spring Boot Starter -->
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>2.2.2</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>

            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>dockerfile-maven-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>pl.project13.maven</groupId>
                <artifactId>git-commit-id-plugin</artifactId>
                <configuration>
                    <offline>true</offline>
                </configuration>
            </plugin>

            <!-- Maven编译插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <source>11</source>
                    <target>11</target>
                </configuration>
            </plugin>

            <!-- 复制需要加密的类文件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <id>copy-secure-classes</id>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <target>
                                <echo message="Copying secure classes for encryption..."/>
                                <mkdir dir="${project.build.directory}/plain"/>
                                <copy todir="${project.build.directory}/plain" preservelastmodified="true">
                                    <fileset dir="${project.build.outputDirectory}">
                                        <include name="**/secure/**/*.class"/>
                                    </fileset>
                                </copy>
                                <echo message="Copied ${toString:copied.files} files"/>
                            </target>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- 执行类文件加密 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>exec-maven-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <id>encrypt-classes</id>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>java</goal>
                        </goals>
                        <configuration>
                            <mainClass>com.mega.gossipharbor.cloud.exchange.mvnbuild.EncryptTool</mainClass>
                            <arguments>
                                <argument>${project.build.directory}/plain</argument>
                                <argument>${project.build.directory}/encrypted</argument>
                            </arguments>
                            <systemProperties>
                                <systemProperty>
                                    <key>encrypt.key</key>
                                    <value>${encrypt.key}</value>
                                </systemProperty>
                            </systemProperties>
                        </configuration>
                    </execution>

                    <!-- 加密XML文件 -->
                    <execution>
                        <id>encrypt-xml-files</id>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>java</goal>
                        </goals>
                        <configuration>
                            <mainClass>com.mega.gossipharbor.cloud.exchange.mvnbuild.EncryptTool</mainClass>
                            <arguments>
                                <argument>${project.build.outputDirectory}/mapper</argument>
                                <argument>${project.build.directory}/encrypted/mapper</argument>
                            </arguments>
                            <systemProperties>
                                <systemProperty>
                                    <key>encrypt.key</key>
                                    <value>${encrypt.key}</value>
                                </systemProperty>
                            </systemProperties>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- 资源处理插件 - 排除Java源文件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.3.1</version>
                <configuration>
                    <excludes>
                        <exclude>**/*.java</exclude>
                        <exclude>**/secure/**/*.java</exclude>
                    </excludes>
                </configuration>
            </plugin>

            <!-- 从JAR中排除原始的敏感类文件和Java源文件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.3.0</version>
                <configuration>
                    <excludes>
                        <exclude>**/*.java</exclude>
                        <exclude>**/secure/**/*.class</exclude>
                        <exclude>**/secure/**/*.java</exclude>
                        <exclude>mapper/*.xml</exclude>
                    </excludes>
                </configuration>
            </plugin>


            <!-- ProGuard 插件 - 启用代码混淆 -->
            <plugin>
                <groupId>com.github.wvengen</groupId>
                <artifactId>proguard-maven-plugin</artifactId>
                <version>2.7.0</version>
                <executions>
                    <execution>
                        <id>proguard</id>
                        <phase>package</phase>
                        <goals>
                            <goal>proguard</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <obfuscate>true</obfuscate>
                    <outputDirectory>${project.build.directory}</outputDirectory>
                    <!-- 关键：使用参数文件启动 -->
                    <argFile>${project.build.directory}/proguard-args.txt</argFile>
                    <options>
                        <!-- 基本配置 -->
                        <option>-dontwarn</option>
                        <option>-dontshrink</option>
                        <option>-dontoptimize</option>
                        <option>-keepattributes Exceptions,InnerClasses,Signature,Deprecated,
                            SourceFile,LineNumberTable,*Annotation*,EnclosingMethod,LocalVariableTable,LocalVariableTypeTable,MethodParameters
                        </option>
                        <option>-adaptclassstrings</option>

                        <!-- 保持主类不混淆 -->
                        <option>-keep com.mega.gossipharbor.cloud.ExchangeApplication { *; }</option>

                        <!-- 保持secure目录下的所有类不混淆 -->
                        <option>-keep com.mega.gossipharbor.cloud.secure.** { *; }</option>

                        <!-- 保持关键接口和抽象类不混淆（避免反射调用问题） -->
                        <option>-keep interface com.example.security.KeyProvider { *; }</option>
                        <option>-keep com.mega.gossipharbor.cloud.security.KeyProviderExcept  ion { *; }</option>
                        <option>-keep com.mega.gossipharbor.cloud.security.EncryptedService { *; }</option>
                        <option>-keep com.mega.gossipharbor.cloud.security.EncryptedClassLoader { *; }</option>
                        <option>-keep com.mega.gossipharbor.cloud.config.EncryptedMapperFactoryBean { *; }</option>

                        <!-- 保持Spring框架相关的类和注解不混淆 -->
                        <option>-keep class org.springframework.** { *; }</option>
                        <option>-keep class org.springframework.boot.** { *; }</option>
                        <option>-keep class org.springframework.web.** { *; }</option>
                        <option>-keep class org.springframework.context.** { *; }</option>
                        <option>-keep class org.springframework.beans.** { *; }</option>
                        <option>-keep class org.springframework.core.** { *; }</option>
                        <option>-keep class org.springframework.data.** { *; }</option>
                        <option>-keep class org.springframework.transaction.** { *; }</option>
                        <option>-keep class org.springframework.aop.** { *; }</option>
                        <option>-keep class org.springframework.scheduling.** { *; }</option>

                        <!-- 保持带有Spring注解的类和方法不混淆 -->
                        <option>-keep @org.springframework.stereotype.Component class * { *; }</option>
                        <option>-keep @org.springframework.stereotype.Service class * { *; }</option>
                        <option>-keep @org.springframework.stereotype.Repository class * { *; }</option>
                        <option>-keep @org.springframework.web.bind.annotation.RestController class * { *; }</option>
                        <option>-keep @org.springframework.web.bind.annotation.Controller class * { *; }</option>
                        <option>-keep @org.springframework.boot.autoconfigure.SpringBootApplication class * { *; }</option>
                        <option>-keep @org.springframework.context.annotation.Configuration class * { *; }</option>

                        <!-- 保持带有特定注解的方法不混淆 -->
                        <option>-keepclassmembers class * {
                            @org.springframework.web.bind.annotation.RequestMapping *;
                            @org.springframework.web.bind.annotation.GetMapping *;
                            @org.springframework.web.bind.annotation.PostMapping *;
                            @org.springframework.web.bind.annotation.PutMapping *;
                            @org.springframework.web.bind.annotation.DeleteMapping *;
                            @org.springframework.beans.factory.annotation.Autowired *;
                            @org.springframework.beans.factory.annotation.Value *;
                            @org.springframework.scheduling.annotation.Scheduled *;
                            @org.springframework.context.annotation.Bean *;
                        }</option>

                        <!-- 保持Controller类的所有方法参数名不混淆 -->
                        <option>-keepparameternames</option>
                        <option>-keepclassmembers @org.springframework.web.bind.annotation.RestController class * {
                            public *;
                        }</option>
                        <option>-keepclassmembers @org.springframework.web.bind.annotation.Controller class * {
                            public *;
                        }</option>

                        <!-- 保持接口不混淆 -->
                        <option>-keepnames interface ** { *; }</option>

                        <!-- 保持枚举类不混淆 -->
                        <option>-keepclassmembers enum * { *; }</option>

                        <!-- 保持序列化相关 -->
                        <option>-keepclassmembers class * implements java.io.Serializable {
                            static final long serialVersionUID;
                            private static final java.io.ObjectStreamField[] serialPersistentFields;
                            private void writeObject(java.io.ObjectOutputStream);
                            private void readObject(java.io.ObjectInputStream);
                            java.lang.Object writeReplace();
                            java.lang.Object readResolve();
                        }</option>

                        <!-- 保持MyBatis相关 -->
                        <option>-keep class org.apache.ibatis.** { *; }</option>
                        <option>-keep class org.mybatis.** { *; }</option>

                        <!-- 保持Lombok生成的方法 -->
                        <option>-keep class lombok.** { *; }</option>
                        <option>-keepclassmembers class * {
                            @lombok.* *;
                        }</option>
                    </options>
                    <libs>
                        <lib>${java.home}/lib/jrt-fs.jar</lib>
                    </libs>
                </configuration>
            </plugin>



            <!-- Spring Boot 打包插件 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                        <configuration>
                            <mainClass>com.mega.gossipharbor.cloud.ExchangeApplication</mainClass>
                        </configuration>
                    </execution>
                </executions>
            </plugin>


            


        </plugins>
    </build>

    <!-- Maven Profiles -->

</project>