package com.mega.gossipharbor.cloud.common.aop;

import com.mega.gossipharbor.cloud.common.dto.common.DingTalkSlowSqlStatisticsDTO;
import com.mega.gossipharbor.cloud.common.services.CommonFeishuService;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.cache.CacheKey;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.ParameterMapping;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.apache.ibatis.type.TypeHandlerRegistry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.text.DateFormat;
import java.util.*;
import java.util.regex.Matcher;

//监控并记录慢查询
@Intercepts({@Signature(type = Executor.class, method = "update", args = { MappedStatement.class, Object.class}),
        @Signature(type = Executor.class, method = "query", args = { MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class, CacheKey.class, BoundSql.class}),
        @Signature(type = Executor.class, method = "query", args = { MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class}),
        @Signature(type = Executor.class, method = "queryCursor", args = { MappedStatement.class, Object.class, RowBounds.class})})
@Slf4j
@Component
public class SqlExecuteTimeInterceptor implements Interceptor {

    @Value("${spring.profiles.active}")
    private String env;
    private final StringRedisTemplate stringRedisTemplate8;
    private final CommonFeishuService commonFeiShuService;
    private static final String SQL_SLOW_KEY = "wf:sql:slow:key:";
    private static final HashMap<String, Long> DURATION_SQL = new HashMap<>();

    @Autowired
    public SqlExecuteTimeInterceptor(@Qualifier("stringRedisTemplate8") StringRedisTemplate stringRedisTemplate8, CommonFeishuService commonFeiShuService) {
        this.stringRedisTemplate8 = stringRedisTemplate8;
        this.commonFeiShuService = commonFeiShuService;
    }

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        long begin = System.currentTimeMillis();
        try {
            return invocation.proceed();
        } finally {
            long duration = System.currentTimeMillis() - begin;
            // 判断超时
            if (duration > 1000) {
                try {
                    // 方法入参
                    Object[] args = invocation.getArgs();
                    MappedStatement mappedStatement = (MappedStatement) args[0];
                    String methodName = mappedStatement.getId();
                    if (!DURATION_SQL.containsKey(methodName) || duration > DURATION_SQL.get(methodName).longValue()) {
                        this.stringRedisTemplate8.opsForHash().increment(SQL_SLOW_KEY + env, methodName, 1);
                        // 控制台打印日志
                        BoundSql boundSql = mappedStatement.getBoundSql(args[1]);
                        Configuration configuration = mappedStatement.getConfiguration();
                        // 格式化sql语句，去除换行符，替换参数
                        String sql = showSql(configuration, boundSql);
                        log.error("执行 SQL：[ {} ], 执行耗时[ {} ms ]", sql, duration);
                        //发送钉钉
                        this.commonFeiShuService.feishuBotSQLAsync(new DingTalkSlowSqlStatisticsDTO(methodName, sql, duration));
                    }
                } catch (Exception e) {
                    log.error("Sql Executor Intercept", e);
                }
            }
        }
    }

    private static String getParameterValue(Object obj) {
        String value = null;
        if (obj instanceof String) {
            value = "'" + obj.toString() + "'";
        } else if (obj instanceof Date) {
            DateFormat formatter = DateFormat.getDateTimeInstance(
                    DateFormat.DEFAULT, DateFormat.DEFAULT, Locale.CHINA);
            value = "'" + formatter.format(obj) + "'";
        } else {
            if (obj != null) {
                value = obj.toString();
            } else {
                value = "";
            }

        }
        return value;
    }

    private static String showSql(Configuration configuration, BoundSql boundSql) {
        Object parameterObject = boundSql.getParameterObject();
        List<ParameterMapping> parameterMappings = boundSql.getParameterMappings();
        String sql = boundSql.getSql().replaceAll("[\\s]+", " ");
        if (!parameterMappings.isEmpty() && parameterObject != null) {
            TypeHandlerRegistry typeHandlerRegistry = configuration.getTypeHandlerRegistry();
            if (typeHandlerRegistry.hasTypeHandler(parameterObject.getClass())) {
                sql = sql.replaceFirst("\\?",
                        Matcher.quoteReplacement(getParameterValue(parameterObject)));

            } else {
                MetaObject metaObject = configuration
                        .newMetaObject(parameterObject);
                for (ParameterMapping parameterMapping : parameterMappings) {
                    String propertyName = parameterMapping.getProperty();
                    if (metaObject.hasGetter(propertyName)) {
                        Object obj = metaObject.getValue(propertyName);
                        sql = sql.replaceFirst("\\?", Matcher.quoteReplacement(getParameterValue(obj)));
                    } else if (boundSql.hasAdditionalParameter(propertyName)) {
                        Object obj = boundSql
                                .getAdditionalParameter(propertyName);
                        sql = sql.replaceFirst("\\?", Matcher.quoteReplacement(getParameterValue(obj)));
                    } else {
                        sql = sql.replaceFirst("\\?", "缺失");
                    }//打印出缺失，提醒该参数缺失并防止错位
                }
            }
        }
        return sql;
    }

    private String formatSQL(String sql, Object parameterObject, List<ParameterMapping> parameterMappingList) {
        if (sql == null || sql.length() == 0) {
            return "";
        }
        // 去除换行符
        sql = sql.replaceAll("[\\s\n ]+", "  ");
        // 替换参数
        Map<String, Object> params = (Map<String, Object>) parameterObject;
        for (ParameterMapping pm : parameterMappingList) {
            if (pm.getMode().name().equals("IN")) {
                sql = sql.replaceFirst("\\?", params.get(pm.getProperty()).toString());
            }
        }
        return sql;
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {

    }
}