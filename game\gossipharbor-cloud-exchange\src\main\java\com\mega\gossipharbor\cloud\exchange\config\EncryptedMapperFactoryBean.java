package com.mega.gossipharbor.cloud.exchange.config;


import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.builder.xml.XMLMapperBuilder;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.mapper.MapperFactoryBean;
import org.springframework.beans.factory.annotation.Autowired;

import com.mega.gossipharbor.cloud.exchange.security.EncryptedClassLoader;
import com.mega.gossipharbor.cloud.exchange.security.EncryptedService;

import java.io.InputStream;

/**
 * 加密Mapper工厂Bean
 * 
 * 扩展标准的MapperFactoryBean，支持加密XML文件的加载
 */
@Slf4j
public class EncryptedMapperFactoryBean<T> extends MapperFactoryBean<T> {

    private EncryptedClassLoader encryptedClassLoader;

    public void setEncryptedClassLoader(EncryptedClassLoader encryptedClassLoader) {
        this.encryptedClassLoader = encryptedClassLoader;
    }

    @Autowired
    @Override
    public void setSqlSessionFactory(SqlSessionFactory sqlSessionFactory) {
        super.setSqlSessionFactory(sqlSessionFactory);
    }

    @Override
    protected void checkDaoConfig() {
        super.checkDaoConfig();

        log.info("🔧 [ENCRYPTED-MAPPER-FACTORY] Configuring encrypted mapper: {}", getMapperInterface().getName());

        try {
            // 加载加密的XML映射文件
            loadEncryptedXmlMapping();
            log.info("✅ [ENCRYPTED-MAPPER-FACTORY] Encrypted resource loader configured for: {}", getMapperInterface().getName());
        } catch (Exception e) {
            log.error("❌ [ENCRYPTED-MAPPER-FACTORY] Failed to load encrypted XML mapping for: {}", getMapperInterface().getName(), e);
            throw new RuntimeException("Failed to load encrypted XML mapping", e);
        }
    }

    /**
     * 加载加密的XML映射文件
     */
    private void loadEncryptedXmlMapping() throws Exception {
        if (encryptedClassLoader == null) {
            log.warn("⚠️ [ENCRYPTED-MAPPER-FACTORY] EncryptedClassLoader not set, skipping XML loading");
            return;
        }

        // 获取Mapper接口名称，并转换为对应的XML文件名
        String mapperClassName = getMapperInterface().getName();
        String simpleName = mapperClassName.substring(mapperClassName.lastIndexOf('.') + 1);

        // 将Dao后缀替换为Mapper后缀（TestDao -> TestMapper）
        String xmlFileName;
        if (simpleName.endsWith("Dao")) {
            xmlFileName = simpleName.substring(0, simpleName.length() - 3) + "Dao.xml";
        } else {
            xmlFileName = simpleName + ".xml";
        }

        log.info("🔍 [ENCRYPTED-MAPPER-FACTORY] Looking for encrypted XML: {}", xmlFileName);

        // 检查是否存在加密的XML文件
        boolean hasXml = EncryptedService.getInstance().hasEncryptedXml(xmlFileName);
        log.info("🔍 [ENCRYPTED-MAPPER-FACTORY] XML file exists: {} -> {}", xmlFileName, hasXml);

        if (hasXml) {
            log.info("🔐 [ENCRYPTED-MAPPER-FACTORY] Loading encrypted XML: {}", xmlFileName);

            // 加载并解密XML文件
            try (InputStream xmlStream = EncryptedService.getInstance().loadEncryptedXml(xmlFileName)) {
                if (xmlStream != null) {
                    log.info("🔓 [ENCRYPTED-MAPPER-FACTORY] XML stream loaded successfully: {}", xmlFileName);

                    // 获取MyBatis配置
                    Configuration configuration = getSqlSessionFactory().getConfiguration();
                    log.info("🔧 [ENCRYPTED-MAPPER-FACTORY] MyBatis configuration obtained: {}", configuration.getClass().getName());

                    // 检查是否已经加载过该资源
                    if (configuration.isResourceLoaded(xmlFileName)) {
                        log.warn("⚠️ [ENCRYPTED-MAPPER-FACTORY] Resource already loaded: {}", xmlFileName);
                        // MyBatis不允许重复加载同一个资源，这里只是记录日志
                    }

                    // 使用XMLMapperBuilder解析XML
                    XMLMapperBuilder xmlMapperBuilder = new XMLMapperBuilder(
                        xmlStream,
                        configuration,
                        xmlFileName,
                        configuration.getSqlFragments()
                    );

                    // 解析XML映射
                    log.info("🔄 [ENCRYPTED-MAPPER-FACTORY] Parsing XML mapping: {}", xmlFileName);
                    xmlMapperBuilder.parse();

                    // 验证映射是否成功加载
                    String namespace = getMapperInterface().getName();
                    if (configuration.hasMapper(getMapperInterface())) {
                        log.info("✅ [ENCRYPTED-MAPPER-FACTORY] Mapper registered successfully: {}", namespace);
                    } else {
                        log.warn("⚠️ [ENCRYPTED-MAPPER-FACTORY] Mapper not found in configuration: {}", namespace);
                    }

                    log.info("✅ [ENCRYPTED-MAPPER-FACTORY] Successfully loaded encrypted XML: {}", xmlFileName);
                } else {
                    log.error("❌ [ENCRYPTED-MAPPER-FACTORY] Failed to decrypt XML: {}", xmlFileName);
                }
            }
        } else {
            log.debug("ℹ️ [ENCRYPTED-MAPPER-FACTORY] No encrypted XML found for: {}", xmlFileName);
        }
    }
    
    // 注意：afterPropertiesSet()是final方法，不能覆盖
    // 我们在checkDaoConfig()中进行初始化配置
}
