package com.mega.gossipharbor.cloud.common.exception;

import com.mega.gossipharbor.cloud.core.utils.SpringUtils;
import lombok.Getter;

@Getter
public enum CommonErrorCode {

    DATA_ERROR(8),
    REQUEST_FREQUENT(9),
    REQUEST_DATA_ERROR(10),
    ACCOUNT_OPERATION_BAN(11),
    ACCOUNT_OPERATION_NOT_IN_WHITELIST(12),

    ;

    private final Integer code;

    CommonErrorCode(Integer code) {
        this.code = code;
    }

    public static CommonErrorCode getByCode(Integer code) {
        for (CommonErrorCode accountErrorCode : CommonErrorCode.values()) {
            if (accountErrorCode.getCode().equals(code)) {
                return accountErrorCode;
            }
        }
        return null;
    }

    public String getMessage() {
        return SpringUtils.getLocaleMessage(this.code);
    }
}
