# 技术方案设计

## 系统架构

### 整体架构
```mermaid
graph TB
    A[游戏客户端] --> B[GameDataController]
    B --> C[GameDataService]
    C --> D[GameDataDao]
    D --> E[character_game_data]
    D --> F[character_game_data_history]
    
    subgraph "gossipharbor-cloud-data模块"
        B
        C
        D
    end
    
    subgraph "数据库"
        E
        F
    end
```

### 技术栈

- **框架**: Spring Boot 2.x
- **数据访问**: MyBatis
- **数据库**: MySQL 8.0
- **API文档**: Swagger
- **数据源**: 动态数据源切换(@DS注解)
- **事务管理**: Spring Transaction

## 模块设计

### 1. Controller层 (GameDataController)

**职责**: HTTP请求处理、参数验证、响应封装

**接口设计**:
- `POST /api/game/save-data` - 存储游戏数据
- `POST /api/game/fetch-data` - 获取游戏数据

**技术要点**:
- 使用`@RestController`和`@RequestMapping`
- 参数验证使用`@Validated`
- 统一返回`Result<T>`格式
- 集成Swagger文档注解

### 2. Service层 (GameDataService)

**职责**: 业务逻辑处理、事务管理

**核心方法**:
- `saveGameData(GameDataSaveReqVO)` - 保存游戏数据
- `fetchGameData(GameDataFetchReqVO)` - 获取游戏数据

**技术要点**:
- 使用`@Service`注解
- 事务管理`@Transactional`
- 数据源切换`@DS("gameMaster")`
- 异常处理机制

### 3. DAO层 (GameDataDao)

**职责**: 数据访问操作

**核心方法**:
- `insertOrUpdateGameData()` - 插入或更新主表数据
- `insertGameDataHistory()` - 插入历史记录
- `selectGameDataByCharacterId()` - 查询游戏数据

**技术要点**:
- 使用`@Mapper`注解
- MyBatis XML映射文件
- 支持`INSERT ... ON DUPLICATE KEY UPDATE`

### 4. VO层设计

**GameDataSaveReqVO** (请求参数):
```java
@Data
@ApiModel("游戏数据保存请求")
public class GameDataSaveReqVO {
    @ApiModelProperty("游戏大区ID")
    private Long gameRegionId = 1001L;
    
    @ApiModelProperty("角色ID")
    private Long characterId;
    
    @ApiModelProperty("JSON格式游戏数据")
    private String jsonData;
}
```

**GameDataFetchReqVO** (请求参数):
```java
@Data
@ApiModel("游戏数据获取请求")
public class GameDataFetchReqVO {
    @ApiModelProperty("游戏大区ID")
    private Long gameRegionId;
    
    @ApiModelProperty("角色ID")
    private Long characterId;
    
    @ApiModelProperty("数据键")
    private String dataKey;
}
```

**GameDataFetchRespVO** (响应参数):
```java
@Data
@ApiModel("游戏数据获取响应")
public class GameDataFetchRespVO {
    @ApiModelProperty("JSON格式游戏数据")
    private String jsonData;
}
```

## 数据库设计

### 表结构

**character_game_data** (主表):
- character_id (PK) - 角色ID
- json_data - JSON格式游戏数据
- create_time - 创建时间
- update_time - 更新时间
- delsign - 删除标记

**character_game_data_history** (历史表):
- id (PK, AUTO_INCREMENT) - 历史记录ID
- character_id - 角色ID
- json_data - JSON格式游戏数据
- create_time - 记录创建时间
- update_time - 记录更新时间
- delsign - 删除标记

### 数据操作策略

**存储操作**:
1. 使用`INSERT ... ON DUPLICATE KEY UPDATE`更新主表
2. 同时插入历史记录到历史表
3. 在同一事务中执行，确保数据一致性

**查询操作**:
1. 根据character_id从主表查询
2. 返回json_data字段内容

## 异常处理

### 错误码设计
- 6001: 角色数据不存在
- 6002: JSON数据格式错误
- 6003: 数据库操作失败

### 异常处理机制
- Service层抛出DataException
- Controller层由GlobalExceptionHandler统一处理
- 返回标准Result错误格式

## 性能考虑

1. **索引优化**: character_id主键索引
2. **JSON存储**: 利用MySQL JSON类型特性
3. **历史表分区**: 可考虑按时间分区优化查询
4. **缓存策略**: 可集成Redis缓存热点数据
