package com.mega.gossipharbor.cloud.common.entity;

import java.util.Date;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@Table(name = "account")
public class Account {
    /**
     * 用户唯一标识(自增)
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY, generator = "SELECT LAST_INSERT_ID()")
    private Long id;

    /**
     * 账号名称(可用于登录)
     */
    @Column(name = "account_name")
    private String accountName;

    /**
     * 用户头像URL
     */
    @Column(name = "avatar")
    private String avatar;

    @Column(name = "lang_tag")
    private String langTag;

    /**
     * 用户所在国家或地区的ISO 3166-1 alpha-3代码，如CHN(中国)、USA(美国)、GBR(英国)
     */
    @Column(name = "location")
    private String location;

    /**
     * 用户时区，默认为北京时区(UTC+8)
     */
    @Column(name = "timezone")
    private String timezone;

    @Column(name = "email")
    private String email;

    /**
     * bcrypt加密后的密码哈希值
     */
    @Column(name = "password")
    private String password;

    /**
     * 手机区号，默认86(中国)
     */
    @Column(name = "phone_area")
    private String phoneArea;

    @Column(name = "phone_number")
    private Long phoneNumber;

    /**
     * 最后登录时间
     */
    @Column(name = "last_login_time")
    private Date lastLoginTime;

    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "delsign")
    private Byte delsign;
}