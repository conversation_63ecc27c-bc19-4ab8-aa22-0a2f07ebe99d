@echo off
chcp 65001 >nul
echo ========================================
echo GossipHarbor Exchange 加密应用启动脚本
echo ========================================
echo 版本: v2.0 (基于调试经验优化)
echo 更新内容:
echo   - 修复Maven依赖解析问题(${revision}变量)
echo   - 跳过ProGuard和Docker构建避免Windows兼容性问题
echo   - 使用端口8080避免端口冲突
echo   - 添加完整的构建和验证流程
echo   - 优化构建性能(避免不必要的缓存清理)
echo ========================================
echo.

REM 环境检查
echo 检查运行环境...
java -version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Java环境，请安装JDK 11或更高版本
    pause
    exit /b 1
)

mvn -version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Maven，请安装Maven 3.6或更高版本
    pause
    exit /b 1
)

for /f "tokens=3" %%i in ('java -version 2^>^&1 ^| findstr "version"') do set java_version=%%i
echo Java版本: %java_version%

for /f "tokens=3" %%i in ('mvn -version 2^>^&1 ^| findstr "Apache Maven"') do set mvn_version=%%i
echo Maven版本: %mvn_version%
echo.

REM 执行构建
echo 开始构建项目...
echo 注意: 使用优化的构建流程，避免不必要的缓存清理
cd ..\..

REM 检查依赖模块是否已存在(避免不必要的重复构建)
if exist "%USERPROFILE%\.m2\repository\com\mega\gossipharbor\cloud\gossipharbor-cloud-common\1.0\gossipharbor-cloud-common-1.0.jar" (
    echo 依赖模块已存在，直接构建exchange模块
) else (
    echo 依赖模块不存在，但继续尝试构建(可能已在本地仓库中)
)

REM 使用reactor构建方式构建exchange模块(解决依赖解析问题)
REM 不使用clean避免文件锁定问题，跳过ProGuard和Docker避免Windows兼容性问题
echo 执行Maven构建...
mvn package -pl :gossipharbor-cloud-exchange -am -DskipTests -Ddockerfile.skip=true -Dproguard.skip=true
if errorlevel 1 (
    echo 错误: Maven构建失败
    echo.
    echo 故障排除提示:
    echo 1. 如果遇到依赖解析问题，请手动执行以下命令:
    echo    mvn install -N
    echo    mvn install -pl base/gossipharbor-cloud-core,base/gossipharbor-cloud-common -DskipTests
    echo 2. 如果遇到文件锁定问题，请关闭IDE后重试
    echo 3. 检查是否有Java进程仍在运行
    echo.
    pause
    exit /b 1
)

cd game\gossipharbor-cloud-exchange

REM 复制加密文件
echo 复制加密文件...
if exist encrypted rmdir /s /q encrypted
xcopy target\encrypted encrypted\ /e /i /q >nul 2>&1
if errorlevel 1 (
    echo 错误: 复制加密文件失败
    pause
    exit /b 1
)

REM 验证构建结果
echo 验证构建结果...
if not exist "target\gossipharbor-cloud-exchange-1.0.jar" (
    echo 错误: 主JAR包不存在 - target\gossipharbor-cloud-exchange-1.0.jar
    echo 请检查Maven构建是否成功
    pause
    exit /b 1
)
echo 主JAR包存在: target\gossipharbor-cloud-exchange-1.0.jar

if not exist "encrypted\com\mega\gossipharbor\cloud\exchange\secure" (
    echo 错误: 加密文件目录不存在 - encrypted\com\mega\gossipharbor\cloud\exchange\secure
    echo 请检查加密过程是否成功
    pause
    exit /b 1
)
echo 加密文件目录存在: encrypted\com\mega\gossipharbor\cloud\exchange\secure

REM 统计加密文件数量
for /f %%i in ('dir /s /b encrypted\*.class 2^>nul ^| find /c /v ""') do set encrypted_class_count=%%i
for /f %%i in ('dir /s /b encrypted\*.xml 2^>nul ^| find /c /v ""') do set encrypted_xml_count=%%i
set /a total_encrypted=%encrypted_class_count%+%encrypted_xml_count%
echo 发现 %encrypted_class_count% 个加密类文件和 %encrypted_xml_count% 个加密XML文件，共 %total_encrypted% 个加密文件

if %total_encrypted% equ 0 (
    echo 警告: 没有发现加密文件，请检查构建过程
    pause
    exit /b 1
)

REM 验证加密文件状态
echo 验证加密文件状态...
echo 注意: 加密文件应该不是标准的Java class格式

echo.
echo 文件结构概览:
echo ├── target\gossipharbor-cloud-exchange-1.0.jar    (主应用JAR包)
echo ├── encrypted\com\mega\gossipharbor\cloud\exchange\secure\  (加密的敏感类文件)
echo │   ├── service\                                   (服务层)
echo │   │   ├── DynamicDataSourceService.class        (AES加密)
echo │   │   └── TransactionService.class               (AES加密)
echo │   └── dao\                                       (数据访问层)
echo │       └── DynamicDataSourceServiceTest.class    (AES加密)
echo ├── encrypted\mapper\                              (加密的XML配置文件)
echo │   └── TransactionDao.xml                         (AES加密)
echo └── 加密ClassLoader将在运行时动态解密并加载这些文件

echo.
echo 构建完成！
echo.
echo 启动模式选择:
echo [1] 生产模式 (使用默认密钥，端口8080)
echo [2] 调试模式 (详细日志输出，端口8080)
echo [3] 自定义密钥模式 (端口8080)
echo [4] 后台运行模式 (端口8080)
set /p mode=请选择启动模式 (1-4):

echo.
echo 启动应用...
echo 应用将在端口8080启动，避免与其他服务的8081端口冲突
echo 启动后可访问 http://localhost:8080/actuator/health 检查应用状态
echo.

if "%mode%"=="1" (
    echo 启动模式: 生产模式 (使用默认密钥)
    echo 命令: java -jar target\gossipharbor-cloud-exchange-1.0.jar --server.port=8080
    java -jar target\gossipharbor-cloud-exchange-1.0.jar --server.port=8080
) else if "%mode%"=="2" (
    echo 启动模式: 调试模式 (详细日志输出)
    echo 命令: java -Dencrypt.debug=true -jar target\gossipharbor-cloud-exchange-1.0.jar --server.port=8080
    java -Dencrypt.debug=true -jar target\gossipharbor-cloud-exchange-1.0.jar --server.port=8080
) else if "%mode%"=="3" (
    set /p custom_key=请输入解密密钥:
    echo 启动模式: 自定义密钥模式
    echo 命令: java -Ddecrypt.key=%custom_key% -jar target\gossipharbor-cloud-exchange-1.0.jar --server.port=8080
    java -Ddecrypt.key=%custom_key% -jar target\gossipharbor-cloud-exchange-1.0.jar --server.port=8080
) else if "%mode%"=="4" (
    echo 启动模式: 后台运行模式
    echo 命令: start /b java -jar target\gossipharbor-cloud-exchange-1.0.jar --server.port=8080
    start /b java -jar target\gossipharbor-cloud-exchange-1.0.jar --server.port=8080 > app.log 2>&1
    echo 应用已在后台启动，日志输出到 app.log
    echo 使用 'type app.log' 查看日志
    echo 使用 'tasklist ^| findstr java' 查看进程
    pause
    goto :end
) else (
    echo 无效选择，使用默认生产模式
    java -jar target\gossipharbor-cloud-exchange-1.0.jar --server.port=8080
)

:end
echo.
echo ========================================
echo GossipHarbor Exchange 应用已退出
echo ========================================
echo.
echo 故障排除提示:
echo 1. 如果遇到端口占用，请检查端口8080是否被其他程序使用
echo 2. 如果遇到依赖解析问题，请删除 %USERPROFILE%\.m2\repository\com\mega\gossipharbor 目录后重试
echo 3. 如果加密文件缺失，请检查Maven构建日志中的加密步骤
echo 4. 应用日志位置: 当前目录下的 app.log 文件(后台模式)
echo.
echo 验证应用状态:
echo - 健康检查: curl http://localhost:8080/actuator/health
echo - 查看进程: tasklist ^| findstr java
echo - 查看端口: netstat -an ^| findstr 8080
echo.
echo 关键改进 (v2.0):
echo - 不清理Maven缓存 (更快的构建)
echo - 不使用clean阶段 (避免文件锁定)
echo - 跳过ProGuard和Docker (Windows兼容性)
echo - 使用端口8080 (避免冲突)
echo - 全面的错误处理和提示
echo.
pause
