package com.mega.gossipharbor.cloud.core.filter;

import org.slf4j.MDC;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

public class TraceIdFilter implements Filter {
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException,
            ServletException {
        try {
            HttpServletRequest req = (HttpServletRequest) request;
            MDC.put("traceId", req.getHeader("trace-id"));
            chain.doFilter(request, response);
        } finally {
            MDC.clear();
        }
    }
}
