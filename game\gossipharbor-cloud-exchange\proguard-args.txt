# ProGuard 参数文件 - 解决 Windows 命令行长度限制问题
# 完整的ProGuard配置，避免Maven插件生成过长命令行

# 基本设置
-dontnote
-ignorewarnings
-dontwarn
-dontshrink
-dontoptimize
-adaptclassstrings
-useuniqueclassmembernames
-dontusemixedcaseclassnames

# 输入输出文件
-injars target/gossipharbor-cloud-exchange-1.0.jar
-outjars target/gossipharbor-cloud-exchange-1.0-obf.jar

# Java运行时库 - 使用绝对路径避免解析问题
-libraryjars "D:/App/FreeJdk/OpenJDK11U-jdk_x64_windows_hotspot_11.0.27_6/jdk-11.0.27+6/lib/jrt-fs.jar"

# 保持属性
-keepattributes *Annotation*,Signature,Exceptions,InnerClasses,SourceFile,LineNumberTable,EnclosingMethod,LocalVariableTable,LocalVariableTypeTable,MethodParameters

# 引入详细的混淆规则
-include proguard.conf