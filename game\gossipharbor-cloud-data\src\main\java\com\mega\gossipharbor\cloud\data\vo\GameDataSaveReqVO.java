package com.mega.gossipharbor.cloud.data.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 游戏数据保存请求VO
 */
@Data
@Accessors(chain = true)
@ApiModel("游戏数据保存请求")
public class GameDataSaveReqVO {

    @ApiModelProperty(value = "游戏大区ID", example = "1001")
    private Long gameRegionId = 1001L;

    @ApiModelProperty(value = "角色ID", example = "123456")
    @NotNull(message = "角色ID不能为空")
    private Long characterId;

    @ApiModelProperty(value = "JSON格式游戏数据", example = "{\"level\":10,\"exp\":1500}")
    @NotNull(message = "游戏数据不能为空")
    private String jsonData;
}
