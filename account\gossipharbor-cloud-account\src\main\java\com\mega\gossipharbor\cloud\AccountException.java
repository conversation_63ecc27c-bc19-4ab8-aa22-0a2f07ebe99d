package com.mega.gossipharbor.cloud;

import com.mega.gossipharbor.cloud.core.exception.BaseException;

import java.util.Objects;

public class AccountException extends BaseException {

    public AccountException(Integer code) {
        this(Objects.requireNonNull(AccountErrorCode.getExchangeCode(code)));
    }

    public AccountException(AccountErrorCode code) {
        super(code.getCode(), code.getMessage());
    }

    public AccountException(Integer code, String message) {
        super(code, message);
    }
}
