package com.mega.gossipharbor.cloud;

import com.mega.gossipharbor.cloud.core.exception.BaseException;

import java.util.Objects;

public class ExchangeException extends BaseException {

    public ExchangeException(Integer code) {
        this(Objects.requireNonNull(ExchangeErrorCode.getExchangeCode(code)));
    }

    public ExchangeException(ExchangeErrorCode code) {
        super(code.getCode(), code.getMessage());
    }

    public ExchangeException(Integer code, String message) {
        super(code, message);
    }
}
