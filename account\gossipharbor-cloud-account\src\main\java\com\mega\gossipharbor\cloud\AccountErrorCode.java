package com.mega.gossipharbor.cloud;

import com.mega.gossipharbor.cloud.core.utils.SpringUtils;
import lombok.Getter;

@Getter
public enum AccountErrorCode {

    ERR_0(0),
    ;

    private final Integer code;

    AccountErrorCode(Integer code) {
        this.code = code;
    }

    public static AccountErrorCode getExchangeCode(Integer code) {
        for (AccountErrorCode exchangeCode : AccountErrorCode.values()) {
            if (exchangeCode.getCode().equals(code)) {
                return exchangeCode;
            }
        }
        return null;
    }

    public String getMessage() {
        return SpringUtils.getLocaleMessage(this.code);
    }
}
