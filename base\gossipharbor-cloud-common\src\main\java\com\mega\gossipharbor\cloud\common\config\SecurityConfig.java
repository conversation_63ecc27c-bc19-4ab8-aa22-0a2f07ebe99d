package com.mega.gossipharbor.cloud.common.config;

import com.mega.gossipharbor.cloud.common.filter.JwtAuthenticationFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

import java.util.List;

@Configuration
@EnableWebSecurity
public class SecurityConfig {
    @Autowired
    private JwtAuthenticationFilter jwtAuthenticationFilter;

    private static final List<String> PUBLIC_ROUTES = List.of(
            "/**/api/**/public/**",
            "/swagger-ui/**",
            "/swagger-resources/**",
            "/v3/api-docs/**",
            "/webjars/swagger-ui/**",
            "/actuator/**",
            "/api/game/**", // TODO 待废弃 gossipharbor-cloud-data 服务游戏数据接口  
            "/data/api/**",      // TODO gossipharbor-cloud-data 服务接口  
            "/exchange/api/**"   // TODO gossipharbor-cloud-exchange 服务接口  
            
    );

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http
                .csrf().disable()
                .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                .and()
                .authorizeRequests(auth -> {
                    // 白名单接口放行
                    for (String path : PUBLIC_ROUTES) {
                        auth.antMatchers(path).permitAll();
                    }
                    // 其余接口需认证
                    auth.anyRequest().authenticated();
                })
                .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }
}
