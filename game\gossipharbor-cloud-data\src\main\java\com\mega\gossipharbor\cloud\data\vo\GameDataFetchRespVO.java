package com.mega.gossipharbor.cloud.data.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 游戏数据获取响应VO
 */
@Data
@Accessors(chain = true)
@ApiModel("游戏数据获取响应")
public class GameDataFetchRespVO {

    @ApiModelProperty(value = "JSON格式游戏数据", example = "{\"level\":10,\"exp\":1500}")
    private String jsonData;
}
