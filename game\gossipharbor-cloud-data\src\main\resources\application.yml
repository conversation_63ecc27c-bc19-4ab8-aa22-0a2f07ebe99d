# 游戏配置
game:
  region-id: 1001  # 默认区域ID，可通过启动参数 --game.region-id=xxx
api:
  docs:
    apis-package: "com.mega.gossipharbor.cloud.data.controller"
    title: Game-Data
    version: 1.0.0
    description: "游戏数据存储获取API文档 gossipharbor-cloud-data"
logging:
  level:
    org.zalando.logbook: trace
    com.mega.platform: info
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} %p [%X{traceId:--}] [%t] %logger{40} : %m%n"
logbook:
  format:
    style: http
  include:
    - /data/**
  write:
    max-body-size: 1024
server:
  port: 8081
  servlet:
    encoding:
      enabled: true
      charset: UTF-8
      force: true
  undertow:
    url-charset: UTF-8
spring:
  profiles:
    active: dev
  application:
    name: gossipharbor-cloud-data-${game.region-id}-${spring.profiles.active}
  messages:
    encoding: UTF-8
    basename: i18n/messages
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  jackson:
    default-property-inclusion: non_null
    deserialization:
      fail_on_unknown_properties: false
mybatis:
  configuration:
    map-underscore-to-camel-case: true
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always
feign:
  httpclient:
    enabled: false
  okhttp:
    enabled: true
sentry:
  dsn: http://7c8a97af00174beda7907f05a114a981@**************:9000/2
  environment: ${spring.profiles.active}
