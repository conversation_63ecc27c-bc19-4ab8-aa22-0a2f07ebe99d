package com.mega.gossipharbor.cloud.core.advice;

import com.mega.gossipharbor.cloud.core.Result;
import com.mega.gossipharbor.cloud.core.ResultCode;
import com.mega.gossipharbor.cloud.core.Results;
import com.mega.gossipharbor.cloud.core.exception.BaseException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;

@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler
    public Result<?> handleBaseException(HttpServletRequest request, BaseException e) {
        log.info("BaseException=[code={},message={}]", e.getCode(), e.getMessage(), e);
        return Results.error(e);
    }

    @ExceptionHandler
    public Result<?> handleMethodArgumentNotValidException(HttpServletRequest request, MethodArgumentNotValidException e) {
        log.info("MethodArgumentNotValidException=[code={},message={}]", ResultCode.PARAM_ERROR.getCode(), ResultCode.PARAM_ERROR.getMessage(), e);
        return Results.error(ResultCode.PARAM_ERROR);
    }

    @ExceptionHandler
    public Result<?> handleBindException(HttpServletRequest request, BindException e) {
        log.info("BindException=[code={},message={}]", ResultCode.PARAM_ERROR.getCode(), ResultCode.PARAM_ERROR.getMessage(), e);
        return Results.error(ResultCode.PARAM_ERROR);
    }

    @ExceptionHandler
    public Result<?> handleException(HttpServletRequest request, Exception e) {
        log.info("Exception=[code={},message={}]", ResultCode.ERROR.getCode(), ResultCode.ERROR.getMessage(), e);
        return Results.error(ResultCode.ERROR);
    }
}
