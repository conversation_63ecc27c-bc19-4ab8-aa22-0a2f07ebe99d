package com.mega.gossipharbor.cloud;

import com.mega.gossipharbor.cloud.core.exception.BaseException;

import java.util.Objects;

public class MonitorException extends BaseException {

    public MonitorException(Integer code) {
        this(Objects.requireNonNull(MonitorErrorCode.getExchangeCode(code)));
    }

    public MonitorException(MonitorErrorCode code) {
        super(code.getCode(), code.getMessage());
    }

    public MonitorException(Integer code, String message) {
        super(code, message);
    }
}
