spring:
  data:
    mongodb:
      uri: mongodb://werewolf-cloud:Mangosteen0!@**************:27017/werewolf?maxPoolSize=500&minPoolSize=10
  cloud:
    consul:
      enabled: false
  datasource:
    master:
      driver-class-name: com.mysql.jdbc.Driver
      jdbc-url: ******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      username: admin
      password: Mangosteen0!
      maximum-pool-size: 4
    slave:
      enabled: true
      driver-class-name: com.mysql.jdbc.Driver
      jdbc-url: ******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      username: admin
      password: Mangosteen0!
      maximum-pool-size: 4
    game-master:
      enabled: true
      driver-class-name: com.mysql.jdbc.Driver
      jdbc-url: ***********************************************************-${game.region-id}?useUnicode=true&characterEncoding=UTF-8&useSSL=false&autoReconnect=true&failOverReadOnly=false&cachePrepStmts=true&prepStmtCacheSize=256&prepStmtCacheSqlLimit=2048&useServerPrepStmts=true&useLocalSessionState=true&rewriteBatchedStatements=true&cacheResultSetMetadata=true&cacheServerConfiguration=true&elideSetAutoCommits=true&maintainTimeStats=false&zeroDateTimeBehavior=convertToNull
      username: admin
      password: Mangosteen0!
      maximum-pool-size: 4
    game-slave:
      enabled: true
      driver-class-name: com.mysql.jdbc.Driver
      jdbc-url: ***********************************************************-${game.region-id}?useUnicode=true&characterEncoding=UTF-8&useSSL=false&autoReconnect=true&failOverReadOnly=false&cachePrepStmts=true&prepStmtCacheSize=256&prepStmtCacheSqlLimit=2048&useServerPrepStmts=true&useLocalSessionState=true&rewriteBatchedStatements=true&cacheResultSetMetadata=true&cacheServerConfiguration=true&elideSetAutoCommits=true&maintainTimeStats=false&zeroDateTimeBehavior=convertToNull
      username: admin
      password: Mangosteen0!
      maximum-pool-size: 4
  redis:
    db0:
      host: **************
      port: 6379
      database: 0
      password: LA1954b!
    db3:
      host: **************
      port: 6379
      database: 3
      password: LA1954b!
    db8:
      host: **************
      port: 6379
      database: 8
      password: LA1954b!
    db14:
      host: **************
      port: 6379
      database: 14
      password: LA1954b!
  kafka:
    bootstrap-servers: **************:9992
    producer:
      acks: 0
      retries: 0
      properties:
        linger.ms: 1000
mybatis:
  configuration.log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
app:
  token:
    key: app_1752202145
    secret: 20b14c39e9094dd3aa262cc6a0f03789