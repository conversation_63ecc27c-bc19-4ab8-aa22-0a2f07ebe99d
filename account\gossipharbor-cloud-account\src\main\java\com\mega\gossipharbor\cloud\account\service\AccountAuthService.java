package com.mega.gossipharbor.cloud.account.service;

import com.mega.gossipharbor.cloud.AccountErrorCode;
import com.mega.gossipharbor.cloud.AccountException;
import com.mega.gossipharbor.cloud.common.entity.Account;
import com.mega.gossipharbor.cloud.common.mapper.AccountMapper;
import com.mega.gossipharbor.cloud.common.utils.JwtTokenUtil;
import com.mega.gossipharbor.cloud.common.vo.AuthLoginByPhoneReqVO;
import com.mega.gossipharbor.cloud.common.vo.AuthLoginRespVO;
import com.mega.gossipharbor.cloud.common.vo.BaseAccountReqVO;
import com.mega.platform.cloud.client.auth.AuthVerificationClient;
import com.mega.platform.cloud.data.vo.auth.AuthVerifySmsCodeReqVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

@Slf4j
@Service
@RequiredArgsConstructor
public class AccountAuthService {
    private final AccountMapper accountMapper;
    private final AuthVerificationClient authVerificationClient;
    private final JwtTokenUtil jwtTokenUtil;


    public AuthLoginRespVO loginOrRegisterByPhone(AuthLoginByPhoneReqVO reqVO) {
        String areaCode = reqVO.getAreaCode();
        String phone = reqVO.getPhone();
        String verificationCode = reqVO.getVerificationCode();
        // 1. 校验验证码
        AuthVerifySmsCodeReqVO authVerifySmsCodeReqVO = new AuthVerifySmsCodeReqVO();
        authVerifySmsCodeReqVO.setAreaCode(areaCode);
        authVerifySmsCodeReqVO.setPhoneNum(phone);
        authVerifySmsCodeReqVO.setCode(verificationCode);
        authVerifySmsCodeReqVO.setClientIp("1");
        authVerifySmsCodeReqVO.setDeviceId("1");
        boolean valid = authVerificationClient.verifySmsCode(authVerifySmsCodeReqVO).getData();
        if (!valid) {
            throw new AccountException(AccountErrorCode.ERR_0);
        }

        // 2. 查询账号是否已存在
        Account account = accountMapper.selectOne(new Account().setPhoneArea(areaCode).setPhoneNumber(Long.valueOf(phone)));

        if (account == null) {
            // 3. 未注册则自动注册
            account = new Account();
            account.setPhoneNumber(Long.valueOf(phone));
            account.setPhoneArea(areaCode);
            account.setAccountName(phone);
            account.setLangTag("en");
            account.setLocation("CHN");
            account.setTimezone("Asia/Shanghai");
            account.setCreateTime(new Date());
            account.setUpdateTime(new Date());

            accountMapper.insertSelective(account);
        }
        BaseAccountReqVO baseAccountReqVO = new BaseAccountReqVO();
        baseAccountReqVO.setAccountId(account.getId());
        // 4. 生成登录 token
        String token = jwtTokenUtil.generateToken(baseAccountReqVO);

        // 5. 构造返回
        AuthLoginRespVO respVO = new AuthLoginRespVO();
        respVO.setToken(token);
        return respVO;
    }

}
