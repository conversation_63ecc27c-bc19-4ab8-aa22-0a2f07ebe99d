package com.mega.gossipharbor.cloud.exchange.security;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 加密服务单例类
 * 
 * 统一管理所有加密解密操作，包括：
 * 1. 密钥管理
 * 2. 文件解密（XML、Class等）
 * 3. 数据解密
 */
@Slf4j
public class EncryptedService {
    
    private static final String ALGORITHM = "AES";
    private static final String ENCRYPTED_DIR = "encrypted";
    
    private final KeyProvider keyProvider;
    private final Path encryptedDir;
    private final boolean debugMode;
    
    private static volatile EncryptedService instance;
    
    public static EncryptedService getInstance() {
        if (instance == null) {
            synchronized (EncryptedService.class) {
                if (instance == null) {
                    instance = new EncryptedService();
                }
            }
        }
        return instance;
    }
    
    public EncryptedService() {
        this.keyProvider = new DefaultKeyProvider();
        this.encryptedDir = Paths.get(ENCRYPTED_DIR);
        this.debugMode = Boolean.parseBoolean(System.getProperty("encrypt.debug", "false"));
        
        if (debugMode) {
            log.debug("🔐 [ENCRYPTED-SERVICE] Initialized:");
            log.debug("  - Encrypted dir: {}", encryptedDir.toAbsolutePath());
            log.debug("  - Key provider: {}", keyProvider.getClass().getSimpleName());
        }
    }

    
    /**
     * 加密字节数组数据
     *
     * @param plainData 原始数据
     * @return 加密后的数据
     * @throws Exception 加密失败时抛出
     */
    public byte[] encrypt(byte[] plainData) throws Exception {
        String keyStr = keyProvider.getDecryptionKey();
        byte[] keyBytes = prepareKey(keyStr);

        Cipher cipher = Cipher.getInstance(ALGORITHM);
        SecretKeySpec keySpec = new SecretKeySpec(keyBytes, ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, keySpec);

        return cipher.doFinal(plainData);
    }

    /**
     * 解密字节数组数据
     *
     * @param encryptedData 加密的数据
     * @return 解密后的数据
     * @throws Exception 解密失败时抛出
     */
    public byte[] decrypt(byte[] encryptedData) throws Exception {
        String keyStr = keyProvider.getDecryptionKey();
        byte[] keyBytes = prepareKey(keyStr);

        Cipher cipher = Cipher.getInstance(ALGORITHM);
        SecretKeySpec keySpec = new SecretKeySpec(keyBytes, ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, keySpec);

        return cipher.doFinal(encryptedData);
    }
    
    /**
     * 加载加密的XML文件并返回解密后的InputStream
     * 
     * @param xmlPath XML文件路径
     * @return 解密后的InputStream，如果文件不存在或解密失败返回null
     */
    public InputStream loadEncryptedXml(String xmlPath) {
        try {
            log.debug("🔍 [ENCRYPTED-SERVICE] Loading encrypted XML: {}", xmlPath);
            
            // 构建加密文件路径
            Path encryptedXmlPath = encryptedDir.resolve("mapper").resolve(xmlPath);
            
            if (!Files.exists(encryptedXmlPath)) {
                log.warn("⚠️ [ENCRYPTED-SERVICE] Encrypted XML file not found: {}", encryptedXmlPath);
                return null;
            }
            
            // 读取并解密文件
            byte[] encryptedData = Files.readAllBytes(encryptedXmlPath);
            log.debug("🔐 [ENCRYPTED-SERVICE] Read encrypted XML file: {} bytes", encryptedData.length);
            
            byte[] decryptedData = decrypt(encryptedData);
            log.debug("✅ [ENCRYPTED-SERVICE] Decrypted XML file: {} bytes", decryptedData.length);
            
            return new ByteArrayInputStream(decryptedData);
            
        } catch (Exception e) {
            log.error("❌ [ENCRYPTED-SERVICE] Failed to load encrypted XML: {}", xmlPath, e);
            return null;
        }
    }
    
    /**
     * 检查是否存在对应的加密XML文件
     * 
     * @param xmlPath XML文件路径
     * @return 文件是否存在
     */
    public boolean hasEncryptedXml(String xmlPath) {
        Path encryptedXmlPath = encryptedDir.resolve("mapper").resolve(xmlPath);
        return Files.exists(encryptedXmlPath);
    }
    
    /**
     * 加载加密的类文件并返回解密后的字节数组
     * 
     * @param className 类名
     * @return 解密后的类字节数组
     * @throws Exception 加载或解密失败时抛出
     */
    public byte[] loadEncryptedClass(String className) throws Exception {
        // 构造加密文件路径
        String classPath = className.replace('.', '/') + ".class";
        Path encryptedFile = encryptedDir.resolve(classPath);
        
        if (debugMode) {
            log.debug("🔍 [ENCRYPTED-SERVICE] Looking for encrypted class: {}", encryptedFile.toAbsolutePath());
        }
        
        if (!Files.exists(encryptedFile)) {
            throw new ClassNotFoundException("Encrypted class file not found: " + encryptedFile);
        }
        
        // 读取加密文件
        byte[] encryptedBytes = Files.readAllBytes(encryptedFile);
        
        if (debugMode) {
            log.debug("🔐 [ENCRYPTED-SERVICE] Read encrypted class file: {} bytes", encryptedBytes.length);
        }
        
        // 解密数据
        byte[] decryptedBytes = decrypt(encryptedBytes);
        
        if (debugMode) {
            log.debug("✅ [ENCRYPTED-SERVICE] Decrypted class to: {} bytes", decryptedBytes.length);
        }
        
        return decryptedBytes;
    }
    
    /**
     * 检查是否存在对应的加密类文件
     * 
     * @param className 类名
     * @return 文件是否存在
     */
    public boolean hasEncryptedClass(String className) {
        try {
            String classPath = className.replace('.', '/') + ".class";
            Path encryptedClassPath = encryptedDir.resolve(classPath);
            return Files.exists(encryptedClassPath);
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 准备密钥字节数组
     * 
     * @param keyStr 密钥字符串
     * @return 准备好的密钥字节数组
     */
    private byte[] prepareKey(String keyStr) {
        try {
            byte[] keyBytes = keyStr.getBytes("UTF-8");
            
            // AES密钥长度必须是16、24或32字节
            if (keyBytes.length == 16 || keyBytes.length == 24 || keyBytes.length == 32) {
                return keyBytes;
            }
            
            // 自动调整密钥长度到16字节
            byte[] adjustedKey = new byte[16];
            if (keyBytes.length < 16) {
                // 密钥太短，用0填充
                System.arraycopy(keyBytes, 0, adjustedKey, 0, keyBytes.length);
                if (debugMode) {
                    log.warn("⚠️ [ENCRYPTED-SERVICE] Key too short, padded with zeros");
                }
            } else {
                // 密钥太长，截断到16字节
                System.arraycopy(keyBytes, 0, adjustedKey, 0, 16);
                if (debugMode) {
                    log.warn("⚠️ [ENCRYPTED-SERVICE] Key too long, truncated to 16 bytes");
                }
            }
            
            return adjustedKey;
        } catch (Exception e) {
            throw new RuntimeException("Failed to prepare decryption key", e);
        }
    }
    
    /**
     * 获取密钥来源信息
     * 
     * @return 密钥来源描述
     */
    public String getKeySource() {
        return keyProvider.getKeySource();
    }
    
    /**
     * 刷新密钥
     * 
     * @throws KeyProviderException 刷新失败时抛出
     */
    public void refreshKey() throws KeyProviderException {
        keyProvider.refreshKey();
    }
    
    /**
     * 检查密钥是否需要刷新
     *
     * @return 是否需要刷新
     */
    public boolean needsKeyRefresh() {
        return keyProvider.needsRefresh();
    }


}
