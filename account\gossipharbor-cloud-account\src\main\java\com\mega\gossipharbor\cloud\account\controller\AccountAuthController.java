package com.mega.gossipharbor.cloud.account.controller;

import com.mega.gossipharbor.cloud.account.service.AccountAuthService;
import com.mega.gossipharbor.cloud.common.vo.AuthLoginByPhoneReqVO;
import com.mega.gossipharbor.cloud.common.vo.AuthLoginRespVO;
import com.mega.gossipharbor.cloud.core.Result;
import com.mega.gossipharbor.cloud.core.Results;
import com.mega.platform.cloud.client.auth.AuthVerificationClient;
import com.mega.platform.cloud.data.vo.auth.AuthSendSmsCodeReqVO;
import com.mega.platform.cloud.data.vo.auth.AuthSendSmsCodeRespVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
@Api(tags = "auth验证接口")
@RequestMapping("/account/api/auth")
public class AccountAuthController {
    private final AuthVerificationClient authVerificationClient;
    private final AccountAuthService accountAuthService;
    @ApiOperation("获取手机验证码")
    @PostMapping("/public/sms/code")
    public Result<AuthSendSmsCodeRespVO> sendSmsCode(@RequestBody AuthSendSmsCodeReqVO vo) throws Exception {
        return Results.success(authVerificationClient.sendSmsCode(vo).getData());
    }

    @ApiOperation("手机号验证码注册或登录")
    @PostMapping("/public/sms/login-or-register")
    public Result<AuthLoginRespVO> loginOrRegisterByPhone(@RequestBody AuthLoginByPhoneReqVO reqVO) {
        return Results.success(accountAuthService.loginOrRegisterByPhone(reqVO));
    }
}
