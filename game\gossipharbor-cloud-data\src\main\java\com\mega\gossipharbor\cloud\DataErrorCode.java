package com.mega.gossipharbor.cloud;

import com.mega.gossipharbor.cloud.core.utils.SpringUtils;
import lombok.Getter;

@Getter
public enum DataErrorCode {

    ERR_0(0),
    INVALID_JSON_DATA(6001), // JSON数据格式错误
    DATABASE_OPERATION_FAILED(6002), // 数据库操作失败
    CHARACTER_DATA_NOT_FOUND(6003), // 角色数据不存在
    ;

    private final Integer code;

    DataErrorCode(Integer code) {
        this.code = code;
    }

    public static DataErrorCode getExchangeCode(Integer code) {
        for (DataErrorCode exchangeCode : DataErrorCode.values()) {
            if (exchangeCode.getCode().equals(code)) {
                return exchangeCode;
            }
        }
        return null;
    }

    public String getMessage() {
        return SpringUtils.getLocaleMessage(this.code);
    }
}
