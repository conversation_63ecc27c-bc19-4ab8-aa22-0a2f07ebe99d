spring:
  data:
    mongodb:
      uri: mongodb://werewolf-cloud:Mangosteen0!@**************:27017/werewolf?maxPoolSize=500&minPoolSize=10
  cloud:
    consul:
      enabled: false
  datasource:
    master:
      driver-class-name: com.mysql.jdbc.Driver
      jdbc-url: ******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      username: admin
      password: Mangosteen0!
      maximum-pool-size: 4
    slave:
      enabled: true
      driver-class-name: com.mysql.jdbc.Driver
      jdbc-url: ******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      username: admin
      password: Mangosteen0!
      maximum-pool-size: 4
  redis:
    db0:
      host: **************
      port: 6379
      database: 0
      password: LA1954b!
    db3:
      host: **************
      port: 6379
      database: 3
      password: LA1954b!
    db8:
      host: **************
      port: 6379
      database: 8
      password: LA1954b!
    db14:
      host: **************
      port: 6379
      database: 14
      password: LA1954b!
  kafka:
    bootstrap-servers: **************:9992
    producer:
      acks: 0
      retries: 0
      properties:
        linger.ms: 1000
mybatis:
  configuration.log-impl: org.apache.ibatis.logging.stdout.StdOutImpl