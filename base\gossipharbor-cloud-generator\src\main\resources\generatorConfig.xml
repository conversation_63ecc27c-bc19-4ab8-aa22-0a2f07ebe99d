<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>
    <context id="Game" targetRuntime="MyBatis3Simple" defaultModelType="flat">
        <property name="javaFileEncoding" value="UTF-8"/>

        <plugin type="tk.mybatis.mapper.generator.MapperPlugin">
            <property name="mappers" value="tk.mybatis.mapper.common.Mapper"/>
            <property name="caseSensitive" value="true"/>
            <property name="forceAnnotation" value="true"/>
            <property name="beginningDelimiter" value=""/>
            <property name="endingDelimiter" value=""/>
            <property name="lombok" value="Getter,Setter,Accessors"/>
        </plugin>

        <plugin type="tk.mybatis.mapper.generator.TemplateFilePlugin">
            <property name="targetProject" value="../gossipharbor-cloud-common/src/main/java"/>
            <property name="targetPackage" value="com.mega.gossipharbor.cloud.common.mapper"/>
            <property name="templatePath" value="mapper.ftl"/>
            <property name="mapperSuffix" value="Mapper"/>
            <property name="fileName" value="${tableClass.shortClassName}${mapperSuffix}.java"/>
        </plugin>

        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="****************************************************"
                        userId="admin"
                        password="Mangosteen0!">
        </jdbcConnection>

        <javaModelGenerator targetPackage="com.mega.gossipharbor.cloud.common.entity"
                            targetProject="../gossipharbor-cloud-common/src/main/java">
        </javaModelGenerator>

        <table tableName="account">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
    </context>

    <context id="GameRegion1001" targetRuntime="MyBatis3Simple" defaultModelType="flat">
        <property name="javaFileEncoding" value="UTF-8"/>

        <plugin type="tk.mybatis.mapper.generator.MapperPlugin">
            <property name="mappers" value="tk.mybatis.mapper.common.Mapper"/>
            <property name="caseSensitive" value="true"/>
            <property name="forceAnnotation" value="true"/>
            <property name="beginningDelimiter" value=""/>
            <property name="endingDelimiter" value=""/>
            <property name="lombok" value="Getter,Setter,Accessors"/>
        </plugin>

        <plugin type="tk.mybatis.mapper.generator.TemplateFilePlugin">
            <property name="targetProject" value="../gossipharbor-cloud-common/src/main/java"/>
            <property name="targetPackage" value="com.mega.gossipharbor.cloud.common.mapper"/>
            <property name="templatePath" value="mapper.ftl"/>
            <property name="mapperSuffix" value="Mapper"/>
            <property name="fileName" value="${tableClass.shortClassName}${mapperSuffix}.java"/>
        </plugin>

        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="****************************************************-region-1001"
                        userId="admin"
                        password="Mangosteen0!">
        </jdbcConnection>

        <javaModelGenerator targetPackage="com.mega.gossipharbor.cloud.common.entity"
                            targetProject="../gossipharbor-cloud-common/src/main/java">
        </javaModelGenerator>

        <table tableName="account">
            <generatedKey column="id" sqlStatement="mysql"/>
        </table>
    </context>
</generatorConfiguration>
