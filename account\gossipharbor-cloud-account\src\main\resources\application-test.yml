logging:
  file:
    path: /opt/log/${spring.cloud.client.hostname}/
  logback:
    rollingpolicy:
      max-file-size: 128MB
spring:
  data:
    mongodb:
      uri: mongodb://werewolf-cloud:Mangosteen0!@*************:27017,*************:27018/werewolf?maxPoolSize=500&minPoolSize=10
  cloud:
    consul:
      enabled: true
      host: *************
      port: 9910
      discovery:
        prefer-ip-address: true
        ip-address: *************
        instance-id: ${spring.application.name}-${spring.cloud.consul.discovery.ip-address}-${server.port}
        service-name: ${spring.application.name}
        health-check-critical-timeout: 1m
  datasource:
    master:
      driver-class-name: com.mysql.jdbc.Driver
      jdbc-url: ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      username: admin
      password: Mangosteen0!
      maximum-pool-size: 4
    slave:
      enabled: true
      driver-class-name: com.mysql.jdbc.Driver
      jdbc-url: ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      username: admin
      password: Mangosteen0!
      maximum-pool-size: 4
  redis:
    db0:
      sentinel:
        master: mymaster
        nodes:
          - *************:26379
          - *************:26380
          - *************:26381
      database: 0
      password: LA1954b!
    db3:
      sentinel:
        master: mymaster
        nodes:
          - *************:26379
          - *************:26380
          - *************:26381
      database: 3
      password: LA1954b!
    db8:
      sentinel:
        master: mymaster
        nodes:
          - *************:26379
          - *************:26380
          - *************:26381
      database: 8
      password: LA1954b!
    db14:
      sentinel:
        master: mymaster
        nodes:
          - *************:26379
          - *************:26380
          - *************:26381
      database: 14
      password: LA1954b!
  kafka:
    bootstrap-servers:
      - 172.25.220.245:9992
    producer:
      acks: 0
      retries: 0
      properties:
        linger.ms: 1000
app:
  token:
    key: app_1752202145
    secret: 20b14c39e9094dd3aa262cc6a0f03789
jwt:
  secret: f2c8f801e3944a06b35897e56bb6db88
  expire-seconds: 36000000